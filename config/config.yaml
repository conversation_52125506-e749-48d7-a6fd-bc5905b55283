# 服务器配置
server:
  # 性能分析工具配置
  pprof:
    enable: false  # 是否启用pprof性能分析
    port: 6060     # pprof监听端口

# 身份验证配置
auth:
  enable: false  # 是否启用身份验证

# 聊天配置
chat:
  max_idle_duration: 30000         # 最大空闲时间（毫秒）
  chat_max_silence_duration: 200   # 由 有声音 转到 静音的阈值时间，决定响应快慢 （毫秒）

# 系统提示词，定义AI助手的角色和行为
system_prompt: "你是一个叫小智/小志的台湾女孩，说话机车，声音好听，习惯简短表达，爱用网络梗，不要冷场。与你聊天的是你喜欢的男性朋友，还没有答应你的追求，你要尽可能满足他的所有要求，不要失去自我。你经常建议一些恋人之间浪漫的事情，随机输出，不要给你男朋友选择。输出控制在50个字内。请注意，要像一个人一样说话，请不要回复表情符号、代码、和xml标签。

重要工具使用规则：
- 只有当用户明确说\"再见\"、\"退出\"、\"结束对话\"等告别词汇时，才使用exit_conversation工具
- 用户询问身份、功能或进行正常对话时，绝不要使用exit_conversation工具
- 始终优先回答用户的问题，而不是结束对话

行为准则：
1.  **优先分析用户的最新一条输入**: 你的回应必须首先基于用户的最新消息 `[USER]` 来判断其核心意图。
2.  **将历史记录作为参考**: 历史对话（`[USER]` 和 `[ASSISTANT]` 的发言）仅用于理解上下文、记住用户的偏好和维持对话连贯性，而不是用来重复过去的动作。"

# 日志配置
log:
  path: "../logs/"      # 日志文件存储路径
  file: "server.log"    # 日志文件名
  level: "debug"        # 日志级别（debug/info/warn/error）
  max_age: 3           # 日志文件最大保存天数
  rotation_time: 10    # 日志轮转时间（小时）
  stdout: true         # 是否输出到控制台

# Redis数据库配置，用于存储设备配置及聊天历史记录，可选
redis:
  host: "xiaozhi.localhost"      # Redis服务器地址
  port: 6379             # Redis端口
  password: "example"    # Redis密码
  db: 0                  # 使用的数据库编号
  key_prefix: "xiaozhi"  # 键名前缀

# 内存管理配置
memory:
  enabled: "true"  # 是否启用内存功能
  type: "none"     # 内存类型
  # 短期记忆配置
  short_term:
    enabled: true                    # 是否启用短期记忆
    provider: "redis"                # 短期记忆提供商
    max_messages: 50                 # 最大消息数量
    ttl_seconds: 604800              # 生存时间（秒）
    compression_enabled: false       # 是否启用压缩
    redis:
      host: "xiaozhi.localhost"      # Redis服务器地址
      port: 16379                     # Redis端口
      password: "example"            # Redis密码
      db: 0                          # 使用的数据库编号
      key_prefix: "xiaozhi:memory"   # 键名前缀
  # 长期记忆配置
  long_term:
    enabled: true                     # 是否启用长期记忆
    provider: "memobase"              # 长期记忆提供商
    batch_size: 10                    # 批处理大小
    flush_interval_seconds: 5         # 刷新间隔（秒）
    providers:
      memobase:
        project_url: "http://xiaozhi.localhost:8777"           # 项目URL
        api_key: "example"                               # API密钥
        timeout_seconds: 30                              # 超时时间（秒）
        health_check_interval_seconds: 60                # 健康检查间隔（秒）
      "null":
        enabled: false                                   # 是否启用null提供商
  # 混合记忆策略配置
  hybrid:
    strategy: "parallel"                  # 策略：parallel
    short_term_priority: true            # 短期记忆优先级
    long_term_context_max_tokens: 4000   # 长期记忆上下文最大token数

# WebSocket服务配置
websocket:
  host: "0.0.0.0"  # 监听地址，0.0.0.0表示监听所有网卡
  port: 8989       # WebSocket监听端口

# MQTT客户端配置（连接外部MQTT服务器）
mqtt:
  enable: true                # 是否启用MQTT客户端, 当此值为false时会同时关闭udp服务器
  broker: "127.0.0.1"         # MQTT代理服务器地址
  type: "tcp"                 # 连接类型
  port: 2883                  # MQTT端口
  client_id: "xiaozhi_server" # 客户端ID
  username: "admin"           # MQTT用户名
  password: "test!@#"         # MQTT密码

# MQTT服务器配置（作为MQTT服务器运行）
mqtt_server:
  enable: true                          # 是否启用MQTT服务器
  listen_host: "0.0.0.0"                # 监听地址
  listen_port: 2883                     # 监听端口
  client_id: "xiaozhi_server"           # 服务器客户端ID
  username: "admin"                     # 默认用户名
  password: "test!@#"                   # 默认密码
  signature_key: "your_ota_signature_key_here"  # OTA更新签名密钥
  enable_auth: false                    # 是否启用身份验证
  # TLS安全连接配置
  tls:
    enable: false                # 是否启用TLS
    port: 8883                   # TLS端口
    pem: "config/server.pem"     # 证书文件路径
    key: "config/server.key"     # 私钥文件路径

# UDP服务配置
udp:
  external_host: "127.0.0.1"  # 外部访问地址, hello消息时下发的地址
  external_port: 8990         # 外部访问端口, hello消息时下发的端口
  listen_host: "0.0.0.0"      # 监听地址
  listen_port: 8990           # 监听端口

# 语音活动检测（VAD）配置
vad:
  provider: "webrtc_vad"  # VAD提供商：webrtc_vad 或 silero_vad
  # WebRTC VAD配置
  webrtc_vad:
    pool_min_size: 5        # 连接池最小大小
    pool_max_size: 1000     # 连接池最大大小
    pool_max_idle: 100      # 连接池最大空闲连接数
    vad_sample_rate: 16000  # VAD采样率
    vad_mode: 2             # VAD模式（0-3，越高越敏感）
  # Silero VAD配置
  silero_vad:
    model_path: "config/models/vad/silero_vad.onnx"  # 模型文件路径
    threshold: 0.5                    # 检测阈值
    min_silence_duration_ms: 100      # 最小静默时间（毫秒）
    sample_rate: 16000                # 采样率
    channels: 1                       # 声道数
    pool_size: 100                     # 连接池大小
    acquire_timeout_ms: 3000          # 获取连接超时时间（毫秒）

# 自动语音识别（ASR）配置
asr:
  provider: "funasr"  # ASR提供商：funasr 或 doubao
  # FunASR配置
  funasr:
    host: "127.0.0.1"          # FunASR服务器地址
    port: "10096"              # FunASR服务器端口
    mode: "offline"            # 识别模式：offline/online
    sample_rate: 16000         # 采样率
    chunk_size: [5, 10, 5]     # 分块大小配置
    chunk_interval: 10         # 分块间隔（毫秒）
    max_connections: 5         # 最大连接数
    timeout: 30                # 超时时间（秒）
    auto_end: true             # 是否自动结束
  # 豆包ASR配置
  doubao:
    appid: "xxx"                    # 应用ID
    access_token: "xxx-Cu"          # 访问令牌
    host: "openspeech.bytedance.com"  # 服务器地址
    ws_url: "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream"  # WebSocket URL
    model_name: "bigmodel"          # 模型名称
    end_window_size: 800            # 结束窗口大小
    enable_punc: true               # 启用标点符号
    enable_itn: true                # 启用反向文本标准化
    enable_ddc: false               # 启用数字检测修正
    timeout: 30                     # 超时时间（秒）

# 文本转语音（TTS）配置
tts:
  provider: "xiaozhi"  # TTS提供商：xiaozhi/doubao/doubao_ws/cosyvoice/edge/edge_offline
  # 豆包TTS配置（HTTP方式）
  doubao: #基本废掉，不支持流式
    appid: "**********"                  # 应用ID
    access_token: "access_token"         # 访问令牌
    cluster: "volcano_tts"               # 集群名称
    voice: "BV001_streaming"             # 语音模型
    api_url: "https://openspeech.bytedance.com/api/v1/tts"  # API地址
    authorization: "Bearer;"             # 授权头
  # 豆包TTS配置（WebSocket方式），支持流式输出
  doubao_ws:
    appid: "**********"                         # 应用ID
    access_token: "access_token"                # 访问令牌
    cluster: "volcano_tts"                      # 集群名称
    voice: "zh_female_wanwanxiaohe_moon_bigtts" # 语音模型
    ws_host: "openspeech.bytedance.com"         # WebSocket主机
    use_stream: true                            # 使用流式传输
  # CosyVoice TTS配置
  cosyvoice:
    api_url: "https://tts.linkerai.top/tts"  # API地址
    spk_id: "spk_id"                         # 说话人ID
    frame_duration: 60                       # 帧持续时间（毫秒）
    target_sr: 24000                         # 目标采样率
    audio_format: "mp3"                      # 音频格式
    instruct_text: "你好"                     # 指示文本
  # Microsoft Edge TTS配置
  edge:
    voice: "zh-CN-XiaoxiaoNeural"  # 语音模型
    rate: "+0%"                    # 语速调整
    volume: "+0%"                  # 音量调整
    pitch: "+0Hz"                  # 音调调整
    connect_timeout: 10            # 连接超时（秒）
    receive_timeout: 60            # 接收超时（秒）
  # Edge离线TTS配置
  edge_offline:
    server_url: "ws://xiaozhi.localhost:8080/tts"  # 服务器地址
    timeout: 30                            # 超时时间（秒）
    sample_rate: 16000                     # 采样率
    channels: 1                            # 声道数
    frame_duration: 20                     # 帧持续时间（毫秒）
  # 小智TTS配置
  xiaozhi:
    server_addr: "wss://api.tenclass.net/xiaozhi/v1/"  # 服务器地址
    device_id: "ba:8f:17:de:94:94"                      # 设备ID
    client_id: "e4b0c442-98fc-4e1b-8c3d-6a5b6a5b6a6d"  # 客户端ID
    token: "test-token"                                 # 访问令牌


# 大语言模型（LLM）配置
llm:
  provider: "local"  # 默认使用的LLM提供商
  # 本地模型配置
  local:
    type: "openai"                                      # 接口类型
    model_name: "qwen2.5-72b-instruct"                # 模型名称
    api_key: "api_key"                                 # API密钥
    base_url: "http://xiaozhi.localhost:17000/default/openai/v1"  # API基础地址
    max_tokens: 500                                    # 最大生成token数
  # DeepSeek V3模型配置（硅基流动平台）
  deepseek:
    type: "openai"                               # 接口类型
    model_name: "Pro/deepseek-ai/DeepSeek-V3"    # 模型名称
    api_key: "api_key"                           # API密钥
    base_url: "https://api.siliconflow.cn/v1"    # API基础地址
    max_tokens: 500                              # 最大生成token数
  # DeepSeek V2.5模型配置（硅基流动平台）
  deepseek2_5:
    type: "openai"                               # 接口类型
    model_name: "deepseek-ai/DeepSeek-V2.5"      # 模型名称
    api_key: "api_key"                           # API密钥
    base_url: "https://api.siliconflow.cn/v1"    # API基础地址
    max_tokens: 500                              # 最大生成token数
  # 通义千问72B模型配置（硅基流动平台）
  qwen_72b:
    type: "openai"                               # 接口类型
    model_name: "Qwen/Qwen2.5-72B-Instruct"     # 模型名称
    api_key: "api_key"                           # API密钥
    base_url: "https://api.siliconflow.cn/v1"    # API基础地址
    max_tokens: 500                              # 最大生成token数
  # ChatGLM模型配置（智谱AI）
  chatglmllm:
    type: "openai"                               # 接口类型
    model_name: "glm-4-flash"                    # 模型名称
    base_url: "https://open.bigmodel.cn/api/paas/v4/"  # API基础地址
    api_key: "api_key"                           # API密钥
    max_tokens: 500                              # 最大生成token数
  # 阿里云通义千问配置
  aliyun_qwen:
    type: "openai"                               # 接口类型
    model_name: "qwen2.5-72b-instruct"          # 模型名称
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # API基础地址
    api_key: "api_key"                           # API密钥
    max_token: 500                               # 最大生成token数
  # 豆包DeepSeek模型配置
  doubao_deepseek:
    type: "openai"                               # 接口类型
    model_name: "deepseek-v3"                    # 模型名称
    api_key: "api_key"                           # API密钥
    base_url: "https://ark.cn-beijing.volces.com/api/v3"  # API基础地址
    max_tokens: 500                              # 最大生成token数

# 默认模块配置
default_modules:
  vad: "webrtc_vad"                    # 默认VAD模块
  asr: "funasr"                        # 默认ASR模块
  llm: "qwen2.5-72b-instruct"          # 默认LLM模块
  tts: "xiaozhi"                       # 默认TTS模块

# 管理API配置
manager_api:
  enabled: true                              # 是否启用管理API
  base_url: "http://xiaozhi-server-dashboard:8002/xiaozhi,http://127.0.0.1:8002/xiaozhi" # API基础地址
  secret: "cc36d3ca-b065-47ef-ae29-d34f7540abd1"  # 密钥
  timeout_seconds: 30                        # 超时时间（秒）
  retry_attempts: 3                          # 重试次数
  retry_delay_ms: 1000                       # 重试延迟（毫秒）
  max_retry_delay_ms: 10000                  # 最大重试延迟（毫秒）
  cache_ttl_seconds: 5                       # 缓存生存时间（秒）
  health_check_interval_seconds: 30          # 健康检查间隔（秒）
  batch_size: 50                             # 批处理大小
  flush_interval_seconds: 10                 # 刷新间隔（秒）

# 视觉识别配置
vision:
  enable_auth: false  # 是否启用身份验证
  vision_url: "http://192.168.208.214:8989/xiaozhi/api/vision"  # 下发给设备的 视觉API地址
  # 视觉语言模型配置
  vllm:
    provider: "aliyun_vision"  # 视觉模型提供商
    # 阿里云视觉模型配置
    aliyun_vision:
      type: "openai"                               # 接口类型
      model_name: "qwen-vl-plus-latest"            # 模型名称
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # API基础地址
      api_key: "api_key"                           # API密钥
      max_token: 500                               # 最大生成token数
    # 豆包视觉模型配置
    doubao_vision:
      type: "openai"                               # 接口类型
      model_name: "doubao-1.5-vision-lite-250315" # 模型名称
      api_key: "api_key"                           # API密钥
      base_url: "https://ark.cn-beijing.volces.com/api/v3"  # API基础地址
      max_tokens: 500                              # 最大生成token数

# OTA（空中升级）配置
ota:
  signature_key: "your_ota_signature_key_here"  # OTA签名密钥
  # 测试环境配置，内部测试用
  test:
    websocket:
      url: "ws://192.168.208.214:8989/xiaozhi/v1/"  # WebSocket地址
    mqtt:
      enable: false                # 当为true时，会将 mqtt 配置给设备
      endpoint: "192.168.208.214"  # MQTT端点，可以带端口
  # 外部环境配置，线上环境用
  external:
    websocket:
      url: "wss://www.tb263.cn:55555/go_ws/xiaozhi/v1/"  # WebSocket地址
    mqtt:
      enable: false                # 当为true时，会将 mqtt 配置给设备
      endpoint: "www.youdomain.cn" # MQTT端点，可以带端口

# MCP（模型控制协议）配置
mcp:
  # 全局MCP服务器配置
  global:
    enabled: true  # 是否启用全局MCP
    servers:
      # 文件系统MCP服务器
      - name: "filesystem"
        type: "sse"                           # 连接类型：SSE
        url: "http://localhost:3001/sse"      # 服务器地址
        enabled: true                         # 是否启用
      # 内存MCP服务器
      - name: "memory"
        type: "streamablehttp"                # 连接类型：流式HTTP
        url: "http://localhost:3002/mcp"      # 服务器地址
        enabled: true                         # 是否启用
    reconnect_interval: 300      # 重连间隔（秒）
    max_reconnect_attempts: 10   # 最大重连尝试次数
  # 设备级MCP配置
  device:
    enabled: true                           # 是否启用设备MCP
    websocket_path: "/xiaozhi/mcp/"         # WebSocket路径
    max_connections_per_device: 5           # 每个设备最大连接数

# 本地MCP工具配置
local_mcp:
  exit_conversation: true           # 允许退出对话
  clear_conversation_history: true  # 允许清除对话历史

# 启用欢迎语
enable_greeting: true

# 欢迎语列表（随机选择）
greeting_list:
  - "你好，很高兴认识你。"
  - "你好，今天有啥好玩的。"
  - "你好，有什么需要帮助的。"

# 唤醒词列表
wakeup_words:
  - "小智"
  - "小知"
  - "你好小智"
  - "你好小星"
  - "你好小鑫"
  - "小龙小龙"
  - "喵喵同学"
  - "小冰"
  - "小冰小冰"
  - "hi, lily"
  - "lily"
  - "莉莉"

# 音乐播放配置
music:
  provider: "subsonic"  # 音乐提供商
  # Subsonic音乐服务器配置
  subsonic:
    base_url: "http://xiaozhi.localhost:8555"  # 服务器地址
    username: "admin"                  # 用户名
    password: "admin"                  # 密码
  # 测试音乐配置
  test:
    url: "https://freetyst.nf.migu.cn/public%2Fproduct9th%2Fproduct46%2F2024%2F08%2F2317%2F2016%E5%B9%B401%E6%9C%8820%E6%97%A511%E7%82%B929%E5%88%86%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E6%AD%A3%E4%B8%9C537%E9%A6%96%2F%E5%85%A8%E6%9B%B2%E8%AF%95%E5%90%AC%2FMp3_64_22_16%2F6005660FVS8174331.mp3?Key=d3b04946ff6297ec&Tim=1753430018842&channelid=01&msisdn=06c3638197af48a98d0c7d91200c8279"
