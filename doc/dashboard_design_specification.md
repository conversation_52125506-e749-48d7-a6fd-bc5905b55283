# xiaozhi-backend-server Dashboard 设计规范

## 1. 项目概述

### 1.1 背景和目标
基于xiaozhi-backend-server AI语音后端服务，设计开发一套完整的Web管理控制台，实现系统监控、设备管理、AI服务配置、对话分析等核心功能，提升运维效率和业务可视化程度。

### 1.2 核心价值
- **实时监控**: 系统状态、设备连接、AI服务性能可视化
- **智能运维**: 设备管理、配置管理、故障诊断自动化
- **数据分析**: 对话质量、用户行为、业务趋势分析
- **效率提升**: 减少人工干预，提升运维响应速度

## 2. 功能架构设计

### 2.1 功能模块概览

```mermaid
graph TB
    A[Dashboard 管理后台] --> B[监控中心]
    A --> C[设备管理]  
    A --> D[AI服务管理]
    A --> E[对话分析]
    A --> F[系统配置]
    A --> G[用户权限]
    A --> H[日志告警]
    
    B --> B1[系统监控大屏]
    B --> B2[性能指标]
    B --> B3[告警管理]
    
    C --> C1[设备列表]
    C --> C2[设备配置] 
    C --> C3[会话管理]
    C --> C4[设备统计]
    
    D --> D1[模型管理]
    D --> D2[配置管理]
    D --> D3[性能监控]
    D --> D4[API监控]
    
    E --> E1[实时对话]
    E --> E2[历史记录]
    E --> E3[对话分析]
    E --> E4[报表统计]
```

### 2.2 详细功能规格

#### 2.2.1 监控中心

**系统监控大屏**
- 实时连接数统计 (WebSocket/MQTT/UDP)
- 服务健康状态 (ASR/LLM/TTS/VAD)
- 系统资源使用 (CPU/内存/磁盘/网络)
- API请求QPS和响应时间
- 错误率和异常统计

**核心指标展示**
```json
{
  "connections": {
    "websocket": 1250,
    "mqtt": 890,
    "udp": 670,
    "total": 2810
  },
  "ai_services": {
    "asr": {"status": "healthy", "latency": "120ms"},
    "llm": {"status": "healthy", "latency": "580ms"}, 
    "tts": {"status": "healthy", "latency": "200ms"},
    "vad": {"status": "healthy", "latency": "50ms"}
  },
  "system": {
    "cpu_usage": 68.5,
    "memory_usage": 72.3,
    "disk_usage": 45.2,
    "goroutines": 1580
  }
}
```

**告警管理**
- 实时告警推送 (WebSocket)
- 告警级别分类 (Critical/Warning/Info)
- 告警历史查询
- 告警规则配置

#### 2.2.2 设备管理

**设备列表**
```typescript
interface Device {
  deviceId: string;
  deviceMac: string;
  status: 'online' | 'offline' | 'error';
  lastSeen: Date;
  location: string;
  version: string;
  transport: 'websocket' | 'mqtt' | 'udp';
  sessionCount: number;
  totalDuration: number;
}
```

**功能特性**
- 设备状态实时刷新
- 设备分组和标签管理
- 批量操作 (重启/配置/删除)
- 设备详情面板 (配置/日志/统计)

**设备配置管理**
- 个性化AI模型配置
- 系统提示词自定义
- 音频参数调节
- 功能开关控制

#### 2.2.3 AI服务管理

**模型管理界面**
```typescript
interface AIModel {
  id: string;
  name: string;
  type: 'asr' | 'llm' | 'tts' | 'vad';
  provider: string;
  version: string;
  status: 'active' | 'inactive' | 'error';
  config: Record<string, any>;
  performance: {
    avgLatency: number;
    successRate: number;
    qps: number;
  };
}
```

**配置管理**
- 多环境配置切换
- A/B测试支持
- 配置版本管理
- 配置变更审计

**性能监控**
- 模型响应时间分析
- 成功率和错误率统计
- API调用量统计
- 成本分析 (Token消耗)

#### 2.2.4 对话分析

**实时对话监控**
```typescript
interface Conversation {
  sessionId: string;
  deviceId: string;
  startTime: Date;
  duration: number;
  status: 'active' | 'completed' | 'error';
  messages: {
    timestamp: Date;
    type: 'user' | 'assistant';
    content: string;
    audioUrl?: string;
    metadata: {
      asrLatency?: number;
      llmLatency?: number;
      ttsLatency?: number;
    };
  }[];
}
```

**历史记录查询**
- 多维度搜索过滤
- 对话内容全文检索
- 时间范围筛选
- 导出功能

**智能分析**
- 对话质量评分
- 情感趋势分析
- 热门话题统计
- 用户满意度分析

#### 2.2.5 系统配置

**全局配置管理**
- 系统参数在线修改
- 配置项分组管理
- 配置热更新支持
- 配置备份恢复

**用户权限管理**
```typescript
interface User {
  id: string;
  username: string;
  role: 'admin' | 'operator' | 'viewer';
  permissions: Permission[];
  createTime: Date;
  lastLogin: Date;
}

interface Permission {
  resource: string; // devices, configs, logs
  actions: ('read' | 'write' | 'delete')[];
}
```

## 3. UI/UX设计规范

### 3.1 设计原则
- **一致性**: 统一的视觉语言和交互方式
- **效率性**: 减少操作步骤，提升工作效率  
- **可用性**: 符合用户认知习惯，降低学习成本
- **响应性**: 适配多种设备屏幕尺寸

### 3.2 布局结构

```
┌─────────────────────────────────────────────┐
│  Logo  │        导航菜单栏                    │ 用户信息
├─────────────────────────────────────────────┤
│               │                             │
│   侧边菜单     │         主要内容区域          │
│               │                             │
│   - 监控中心   │   ┌─────────────────────┐   │
│   - 设备管理   │   │                     │   │
│   - AI服务    │   │    页面具体内容        │   │
│   - 对话分析   │   │                     │   │
│   - 系统配置   │   │                     │   │
│   - 日志告警   │   └─────────────────────┘   │
│               │                             │
└─────────────────────────────────────────────┘
```

### 3.3 视觉设计

**色彩规范**
- 主色调: #1890ff (Ant Design蓝)
- 成功: #52c41a (绿色)
- 警告: #faad14 (橙色)  
- 错误: #f5222d (红色)
- 中性: #d9d9d9 (灰色)

**图标体系**
- 监控: 📊 仪表盘图标
- 设备: 📱 设备图标
- AI服务: 🤖 机器人图标
- 对话: 💬 对话气泡
- 配置: ⚙️ 齿轮图标
- 日志: 📝 文档图标

### 3.4 交互设计

**导航体验**
- 面包屑导航显示当前位置
- 侧边菜单支持折叠/展开
- 菜单项active状态高亮显示

**数据展示**
- 表格支持排序、筛选、分页
- 图表支持缩放、钻取、导出
- 大数字使用动画过渡效果

**操作反馈**
- 按钮点击loading状态
- 操作成功/失败消息提示
- 危险操作确认对话框

## 4. 技术架构设计

### 4.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[React Web App]
        B[移动端 H5]
    end
    
    subgraph "API网关"
        C[Nginx + API Gateway]
    end
    
    subgraph "应用层"
        D[Dashboard Backend API]
        E[WebSocket Service]
    end
    
    subgraph "核心服务"
        F[xiaozhi-backend-server]
    end
    
    subgraph "数据层" 
        G[Redis Cache]
        H[PostgreSQL]
        I[InfluxDB 时序数据]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    D --> F
    E --> F
    D --> G
    D --> H
    E --> I
    F --> G
```

### 4.2 前端技术栈

**核心框架**
```json
{
  "framework": "React 18",
  "language": "TypeScript 5.0+",
  "build": "Vite 4.0",
  "ui": "Ant Design 5.0",
  "state": "Redux Toolkit + RTK Query",
  "routing": "React Router 6",
  "charts": "ECharts 5.0",
  "websocket": "Socket.IO Client"
}
```

**项目结构**
```
dashboard-frontend/
├── src/
│   ├── components/     # 通用组件
│   ├── pages/          # 页面组件
│   ├── services/       # API服务
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   ├── types/          # 类型定义
│   └── styles/         # 样式文件
├── public/             # 静态资源
├── tests/              # 测试文件
└── docs/               # 文档
```

**关键组件设计**
```typescript
// 实时监控组件
const MonitorDashboard: React.FC = () => {
  const { data: metrics } = useRealtimeMetrics();
  const { data: alerts } = useAlerts();
  
  return (
    <div className="monitor-dashboard">
      <MetricCards metrics={metrics} />
      <ConnectionChart data={metrics.connections} />
      <PerformanceChart data={metrics.performance} />
      <AlertPanel alerts={alerts} />
    </div>
  );
};

// 设备管理组件  
const DeviceManagement: React.FC = () => {
  const [devices, setDevices] = useDevices();
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  
  const handleBatchOperation = (action: string) => {
    // 批量操作逻辑
  };
  
  return (
    <DeviceTable 
      data={devices}
      selection={selectedDevices}
      onSelectionChange={setSelectedDevices}
      onBatchOperation={handleBatchOperation}
    />
  );
};
```

### 4.3 后端技术栈

**技术选型**
```go
// 核心依赖
module dashboard-backend

require (
    github.com/gin-gonic/gin v1.9.1          // Web框架
    github.com/gorilla/websocket v1.5.0      // WebSocket
    github.com/go-redis/redis/v8 v8.11.5     // Redis客户端
    gorm.io/gorm v1.25.2                     // ORM框架
    gorm.io/driver/postgres v1.5.2           // PostgreSQL驱动
    github.com/prometheus/client_golang v1.16.0 // Metrics
)
```

**API设计规范**
```go
// RESTful API设计
type APIResponse struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data"`
    Total   int64       `json:"total,omitempty"`
}

// 设备管理API
func (h *DeviceHandler) GetDevices(c *gin.Context) {
    var req GetDevicesRequest
    if err := c.ShouldBindQuery(&req); err != nil {
        c.JSON(400, APIResponse{Code: 400, Message: err.Error()})
        return
    }
    
    devices, total, err := h.deviceService.GetDevices(c, req)
    if err != nil {
        c.JSON(500, APIResponse{Code: 500, Message: err.Error()})
        return
    }
    
    c.JSON(200, APIResponse{
        Code: 200,
        Message: "success",
        Data: devices,
        Total: total,
    })
}
```

**WebSocket实时数据**
```go
// 实时数据推送
type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
}

func (h *Hub) Run() {
    for {
        select {
        case client := <-h.register:
            h.clients[client] = true
            
        case client := <-h.unregister:
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.send)
            }
            
        case message := <-h.broadcast:
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
        }
    }
}
```

### 4.4 数据库设计

**PostgreSQL主要表结构**
```sql
-- 设备信息表
CREATE TABLE devices (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(64) UNIQUE NOT NULL,
    device_mac VARCHAR(32),
    status VARCHAR(16) DEFAULT 'offline',
    last_seen TIMESTAMP,
    location VARCHAR(255),
    version VARCHAR(32),
    transport VARCHAR(16),
    config JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 对话会话表
CREATE TABLE conversations (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(64) UNIQUE NOT NULL,
    device_id VARCHAR(64) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration INTEGER,
    status VARCHAR(16),
    message_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 对话消息表
CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL,
    message_type VARCHAR(16) NOT NULL, -- 'user', 'assistant'
    content TEXT NOT NULL,
    audio_url VARCHAR(255),
    metadata JSONB,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 系统指标表 (时序数据)
CREATE TABLE metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(64) NOT NULL,
    metric_type VARCHAR(32) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    labels JSONB,
    timestamp TIMESTAMP NOT NULL
);
```

**Redis缓存策略**
```
# 设备状态缓存
devices:status:{device_id} -> {status: "online", last_seen: timestamp}

# 实时指标缓存  
metrics:realtime -> {connections: 1250, qps: 89, ...}

# 会话缓存
sessions:active:{session_id} -> {device_id, start_time, ...}

# 配置缓存
config:global -> {system config json}
config:device:{device_id} -> {device config json}
```

## 5. 部署架构设计

### 5.1 容器化部署

**Docker Compose配置**
```yaml
version: '3.8'
services:
  dashboard-frontend:
    build: ./dashboard-frontend
    ports:
      - "3000:80"
    depends_on:
      - dashboard-backend
    
  dashboard-backend:
    build: ./dashboard-backend  
    ports:
      - "8080:8080"
    environment:
      - POSTGRES_URL=**********************************/dashboard
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
      
  xiaozhi-server:
    image: xiaozhi-server:latest
    ports:
      - "8989:8989"
    volumes:
      - ./config:/app/config
    depends_on:
      - redis
      - postgres
      
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - dashboard-frontend
      - dashboard-backend
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: dashboard
      POSTGRES_USER: admin  
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  postgres_data:
  redis_data:
  grafana_data:
```

### 5.2 生产环境部署

**Kubernetes部署清单**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashboard-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dashboard-backend
  template:
    metadata:
      labels:
        app: dashboard-backend
    spec:
      containers:
      - name: dashboard-backend
        image: dashboard-backend:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: dashboard-secret
              key: postgres-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: dashboard-backend-service
spec:
  selector:
    app: dashboard-backend
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 6. 开发和测试计划

### 6.1 开发阶段规划

**Phase 1: 基础框架 (2周)**
- 前后端项目脚手架搭建
- 基础UI组件库封装
- API框架和中间件开发
- 数据库Schema设计

**Phase 2: 核心功能 (4周)**  
- 监控中心开发
- 设备管理功能
- AI服务管理界面
- WebSocket实时数据

**Phase 3: 高级功能 (3周)**
- 对话分析功能
- 配置管理系统  
- 权限管理
- 日志告警系统

**Phase 4: 优化完善 (2周)**
- 性能优化
- UI/UX优化
- 测试完善
- 文档编写

### 6.2 质量保证

**测试策略**
```javascript
// 前端单元测试
describe('DeviceManagement', () => {
  it('should render device list correctly', () => {
    const mockDevices = [
      { deviceId: 'device1', status: 'online' }
    ];
    
    render(<DeviceManagement devices={mockDevices} />);
    expect(screen.getByText('device1')).toBeInTheDocument();
  });
});

// E2E测试
describe('Dashboard E2E', () => {
  it('should allow user to view device details', async () => {
    await page.goto('/devices');
    await page.click('[data-testid="device-item-1"]');
    await expect(page.locator('.device-detail')).toBeVisible();
  });
});
```

**代码质量控制**
- ESLint + Prettier代码规范  
- Git hooks预提交检查
- 代码覆盖率要求80%+
- SonarQube代码质量扫描

## 7. 运维和监控

### 7.1 应用监控

**关键指标**
- 应用性能: 响应时间、吞吐量、错误率
- 用户行为: 页面访问量、功能使用统计
- 业务指标: 设备在线率、对话成功率

**告警规则**
```yaml
# Prometheus告警规则
groups:
- name: dashboard.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: High error rate detected
      
  - alert: DatabaseConnectionFailed
    expr: up{job="postgres"} == 0
    for: 1m
    annotations:
      summary: Database connection failed
```

### 7.2 日志管理

**日志收集**
- 应用日志: 结构化JSON格式
- 访问日志: Nginx access log  
- 错误日志: 应用异常和系统错误
- 审计日志: 用户操作记录

**日志分析**
- ELK Stack日志分析平台
- 日志聚合和全文检索
- 异常检测和自动告警

## 8. 安全设计

### 8.1 认证授权

**JWT认证**
```go
type JWTClaims struct {
    UserID   string `json:"user_id"`
    Username string `json:"username"`
    Role     string `json:"role"`
    jwt.StandardClaims
}

func GenerateJWT(user *User) (string, error) {
    claims := JWTClaims{
        UserID:   user.ID,
        Username: user.Username,
        Role:     user.Role,
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
            Issuer:    "dashboard",
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}
```

**RBAC权限控制**
```typescript
const usePermission = () => {
  const user = useSelector(selectCurrentUser);
  
  const hasPermission = (resource: string, action: string) => {
    return user.permissions.some(p => 
      p.resource === resource && p.actions.includes(action)
    );
  };
  
  return { hasPermission };
};
```

### 8.2 安全防护

**API安全**
- HTTPS强制加密传输
- API频率限制防止滥用
- 请求参数验证和过滤
- CORS跨域请求控制

**数据安全**  
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证

这个Dashboard将为xiaozhi-backend-server项目提供完整的可视化管理能力，显著提升系统的可运维性和业务洞察能力。建议按照Phase分阶段实施开发。