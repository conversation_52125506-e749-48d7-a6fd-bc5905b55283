# 记忆系统配置指南

## 概述

记忆系统现在支持三种类型的记忆配置：
- **短记忆 (short_term)**：基于 Redis 的快速临时存储
- **长记忆 (long_term)**：基于向量数据库的智能长期存储
- **混合记忆 (hybrid)**：结合短记忆和长记忆的优势

## 配置选项

### 基础配置

```json
{
  "memory": {
    "enabled": true,
    "type": "hybrid"
  }
}
```

### 短记忆配置

```json
{
  "memory": {
    "enabled": true,
    "type": "short_term",
    "short_term": {
      "enabled": true,
      "max_messages": 100,
      "ttl": 604800,
      "compression_enabled": false
    }
  },
  "redis": {
    "key_prefix": "xiaozhi"
  }
}
```

### 长记忆配置

```json
{
  "memory": {
    "enabled": true,
    "type": "long_term"
  },
  "long_term_memory": {
    "enabled": true,
    "provider": "memobase"
  },
  "memobase": {
    "enabled": true,
    "project_url": "https://your-memobase-url.com",
    "api_key": "your-api-key"
  }
}
```

### 混合记忆配置（推荐）

```json
{
  "memory": {
    "enabled": true,
    "type": "hybrid",
    "short_term": {
      "enabled": true,
      "max_messages": 50,
      "ttl": 86400,
      "compression_enabled": false
    }
  },
  "long_term_memory": {
    "enabled": true,
    "provider": "memobase"
  },
  "memobase": {
    "enabled": true,
    "project_url": "https://your-memobase-url.com",
    "api_key": "your-api-key"
  },
  "redis": {
    "key_prefix": "xiaozhi"
  }
}
```

## 配置参数说明

### 通用参数

- `memory.enabled`: 是否启用记忆功能
- `memory.type`: 记忆类型，可选值：`short_term`、`long_term`、`hybrid`

### 短记忆参数

- `memory.short_term.enabled`: 是否启用短记忆
- `memory.short_term.max_messages`: 最大存储消息数量
- `memory.short_term.ttl`: 消息过期时间（秒）
- `memory.short_term.compression_enabled`: 是否启用压缩（暂未实现）
- `redis.key_prefix`: Redis 键前缀

### 长记忆参数

- `long_term_memory.enabled`: 是否启用长记忆
- `long_term_memory.provider`: 长记忆提供者，目前支持 `memobase`
- `memobase.enabled`: 是否启用 Memobase
- `memobase.project_url`: Memobase 项目 URL
- `memobase.api_key`: Memobase API 密钥

## 使用示例

### 代码中使用新的记忆系统

```go
package main

import (
    "context"
    "xiaozhi-esp32-server-golang/internal/domain/llm/memory"
    "github.com/cloudwego/eino/schema"
)

func main() {
    // 初始化记忆系统（使用配置文件中的设置）
    err := memory.Init()
    if err != nil {
        panic(err)
    }
    defer memory.Get().Close()

    // 获取记忆实例
    mem := memory.Get()
    
    // 检查记忆系统状态
    fmt.Printf("记忆类型: %s\n", mem.GetMemoryType())
    fmt.Printf("健康状态: %t\n", mem.IsMemoryHealthy())

    // 添加消息
    ctx := context.Background()
    deviceID := "device_001"
    
    msg := schema.Message{
        Role:    schema.User,
        Content: "你好，我是用户张三",
    }
    
    err = mem.AddMessage(ctx, deviceID, msg)
    if err != nil {
        panic(err)
    }

    // 获取适用于 LLM 的消息
    messages, err := mem.GetMessagesForLLM(ctx, deviceID, 10)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("获取到 %d 条消息\n", len(messages))
}
```

### 使用特定配置创建记忆实例

```go
package main

import (
    "xiaozhi-esp32-server-golang/internal/domain/llm/memory"
)

func main() {
    // 创建短记忆配置
    config := &memory.MemoryConfig{
        Type:    memory.ShortTermMemoryType,
        Enabled: true,
        ShortTermConfig: &memory.ShortTermConfig{
            Enabled:     true,
            KeyPrefix:   "test",
            MaxMessages: 50,
            TTL:         3600,
        },
    }

    // 创建记忆实例
    mem, err := memory.NewMemoryWithConfig(config)
    if err != nil {
        panic(err)
    }
    defer mem.Close()

    // 使用记忆实例...
}
```

## 迁移指南

从旧版本记忆系统迁移到新版本：

1. **配置迁移**：
   - 旧版本的配置会自动兼容
   - 建议添加新的 `memory.type` 配置项

2. **代码迁移**：
   - 现有的 API 保持不变
   - 新增了一些状态检查方法

3. **性能优化**：
   - 混合模式下，短记忆和长记忆并行工作
   - 长记忆采用异步批量处理，不会影响响应速度

## 故障排除

### 常见问题

1. **记忆系统初始化失败**
   - 检查 Redis 连接配置
   - 检查长记忆提供者配置

2. **长记忆无法工作**
   - 验证 Memobase 配置
   - 检查网络连接

3. **性能问题**
   - 调整短记忆的 `max_messages` 参数
   - 检查 Redis 性能

### 日志分析

记忆系统会输出详细的日志信息：

```
记忆系统初始化成功，类型: hybrid(short_term_redis,long_term_memobase), 健康状态: true
```

查看日志可以了解当前记忆系统的工作状态。

## 最佳实践

1. **生产环境推荐使用混合模式**
2. **根据业务需求调整短记忆的 TTL 和最大消息数**
3. **定期检查记忆系统的健康状态**
4. **在高并发场景下监控 Redis 性能**