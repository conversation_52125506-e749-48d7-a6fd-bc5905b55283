# Memory 配置系统优化文档

## 概述

经过重构优化，Memory 系统现在采用了全新的配置结构，所有记忆相关配置统一整合到 `memory` 配置块下，提供了更清晰、更易维护的配置体验。

## 配置结构

### 完整配置示例

```json
{
  "memory": {
    "enabled": true,
    "type": "hybrid",
    "short_term": {
      "enabled": true,
      "provider": "redis",
      "max_messages": 50,
      "ttl_seconds": 604800,
      "compression_enabled": false,
      "redis": {
        "host": "127.0.0.1",
        "port": 6379,
        "password": "example",
        "db": 0,
        "key_prefix": "xiaozhi:memory"
      }
    },
    "long_term": {
      "enabled": true,
      "provider": "memobase",
      "batch_size": 10,
      "flush_interval_seconds": 5,
      "providers": {
        "memobase": {
          "project_url": "http://127.0.0.1:8777",
          "api_key": "example",
          "timeout_seconds": 30,
          "health_check_interval_seconds": 60
        },
        "null": {
          "enabled": false
        }
      }
    },
    "hybrid": {
      "strategy": "parallel",
      "short_term_priority": true,
      "long_term_context_max_tokens": 4000
    }
  }
}
```

## 配置说明

### 1. 主配置 (`memory`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `enabled` | boolean | 否 | `true` | 是否启用记忆功能 |
| `type` | string | 否 | `"hybrid"` | 记忆类型：`short_term`, `long_term`, `hybrid`, `null` |

### 2. 短记忆配置 (`memory.short_term`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `enabled` | boolean | 否 | 根据`type`自动设置 | 是否启用短记忆 |
| `provider` | string | 否 | `"redis"` | 短记忆提供者，目前仅支持 `redis` |
| `max_messages` | integer | 否 | `50` | 最大消息数量 |
| `ttl_seconds` | integer | 否 | `604800` | 消息生存时间（秒），默认7天 |
| `compression_enabled` | boolean | 否 | `false` | 是否启用压缩 |

#### 2.1 Redis 配置 (`memory.short_term.redis`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `host` | string | 是 | - | Redis 主机地址 |
| `port` | integer | 是 | - | Redis 端口 |
| `password` | string | 否 | `""` | Redis 密码 |
| `db` | integer | 否 | `0` | Redis 数据库编号 |
| `key_prefix` | string | 否 | `"xiaozhi:memory"` | Redis 键前缀 |

### 3. 长记忆配置 (`memory.long_term`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `enabled` | boolean | 否 | 根据`type`自动设置 | 是否启用长记忆 |
| `provider` | string | 否 | `"memobase"` | 长记忆提供者 |
| `batch_size` | integer | 否 | `10` | 批处理大小 |
| `flush_interval_seconds` | integer | 否 | `5` | 批处理刷新间隔（秒） |

#### 3.1 Memobase 提供者配置 (`memory.long_term.providers.memobase`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_url` | string | 是 | - | Memobase 项目 URL |
| `api_key` | string | 是 | - | Memobase API 密钥 |
| `timeout_seconds` | integer | 否 | `30` | 请求超时时间（秒） |
| `health_check_interval_seconds` | integer | 否 | `60` | 健康检查间隔（秒） |

### 4. 混合记忆配置 (`memory.hybrid`)

| 字段 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `strategy` | string | 否 | `"parallel"` | 处理策略：`parallel`（并行）, `sequential`（顺序） |
| `short_term_priority` | boolean | 否 | `true` | 获取消息时是否优先使用短记忆 |
| `long_term_context_max_tokens` | integer | 否 | `4000` | 长记忆上下文最大 token 数 |

## 记忆类型详解

### 1. 短记忆 (`short_term`)
- **特点**: 基于 Redis 的快速临时存储
- **适用场景**: 需要快速响应的对话历史
- **优势**: 响应速度快，实时性好
- **配置示例**:
```json
{
  "memory": {
    "enabled": true,
    "type": "short_term",
    "short_term": {
      "enabled": true,
      "max_messages": 30,
      "ttl_seconds": 86400
    }
  }
}
```

### 2. 长记忆 (`long_term`)
- **特点**: 基于向量数据库的智能长期存储
- **适用场景**: 需要用户画像和上下文分析
- **优势**: 智能总结，个性化服务
- **配置示例**:
```json
{
  "memory": {
    "enabled": true,
    "type": "long_term",
    "long_term": {
      "enabled": true,
      "provider": "memobase",
      "providers": {
        "memobase": {
          "project_url": "http://localhost:8777",
          "api_key": "your-api-key"
        }
      }
    }
  }
}
```

### 3. 混合记忆 (`hybrid`) - 推荐
- **特点**: 结合短记忆和长记忆的优势
- **适用场景**: 生产环境，需要平衡性能和智能
- **优势**: 既快速又智能
- **配置示例**:
```json
{
  "memory": {
    "enabled": true,
    "type": "hybrid",
    "short_term": {
      "enabled": true,
      "max_messages": 50
    },
    "long_term": {
      "enabled": true,
      "provider": "memobase"
    },
    "hybrid": {
      "strategy": "parallel",
      "short_term_priority": true
    }
  }
}
```

### 4. 禁用记忆 (`null`)
- **特点**: 完全禁用记忆功能
- **适用场景**: 测试环境或无状态应用
- **配置示例**:
```json
{
  "memory": {
    "enabled": false,
    "type": "null"
  }
}
```

## 配置优化亮点

### 1. 结构清晰
- 所有记忆配置统一在 `memory` 块下
- 层级分明，易于理解和维护
- 支持多种记忆类型灵活切换

### 2. 默认值智能
- 大部分配置都有合理的默认值
- 根据记忆类型自动启用相应的子模块
- 开箱即用，配置简单

### 3. 扩展性强
- 支持多个长记忆提供者
- 混合模式下可灵活配置策略
- 便于后续添加新的记忆类型

### 4. 生产就绪
- 详细的配置验证
- 完善的错误处理
- 支持配置热更新（需重启）

## 迁移指南

### 从旧配置迁移

**旧配置结构:**
```json
{
  "redis": {
    "key_prefix": "xiaozhi"
  },
  "long_term_memory": {
    "enabled": true,
    "provider": "memobase"
  },
  "memobase": {
    "project_url": "http://localhost:8777",
    "api_key": "example"
  }
}
```

**新配置结构:**
```json
{
  "memory": {
    "enabled": true,
    "type": "hybrid",
    "short_term": {
      "enabled": true,
      "redis": {
        "key_prefix": "xiaozhi:memory"
      }
    },
    "long_term": {
      "enabled": true,
      "provider": "memobase",
      "providers": {
        "memobase": {
          "project_url": "http://localhost:8777",
          "api_key": "example"
        }
      }
    }
  }
}
```

## 性能建议

### 1. 短记忆优化
- 根据对话长度调整 `max_messages`
- 合理设置 `ttl_seconds` 避免内存浪费
- 生产环境建议关闭 `compression_enabled`

### 2. 长记忆优化
- 调整 `batch_size` 平衡性能和实时性
- 根据服务器性能设置 `timeout_seconds`
- 定期监控 `health_check_interval_seconds`

### 3. 混合模式优化
- 设置合适的 `long_term_context_max_tokens`
- 根据业务需求选择 `strategy`
- 考虑网络延迟设置 `short_term_priority`

## 故障排除

### 1. 配置验证失败
- 检查必需字段是否配置
- 确认 URL 格式是否正确
- 验证 API 密钥是否有效

### 2. 连接问题
- 检查 Redis 连接参数
- 确认 Memobase 服务可达性
- 查看网络防火墙设置

### 3. 性能问题
- 监控 Redis 内存使用
- 检查长记忆批处理队列
- 调整并发和超时参数

## 监控和日志

系统会自动记录以下信息：
- 记忆类型初始化状态
- 配置验证结果
- 连接健康检查
- 性能指标统计

建议在生产环境中监控这些指标，及时发现和解决问题。