# WebSocket连接错误处理改进

## 问题描述

原始错误日志：
```
2025-08-18 16:29:33.299 [error] [websocket_conn.go:62] 发送ping消息失败，设备ID: 1a:78:30:64:16:a9, 错误: websocket: close sent
```

这个错误信息缺乏足够的上下文信息来分析问题的根本原因，特别是：
- 无法判断连接关闭是由哪一方发起的
- 缺少连接生命周期信息
- 没有关闭原因的详细记录

## 解决方案

### 1. 增强连接状态跟踪

在`WebSocketConn`结构体中添加了以下字段：

```go
type WebSocketConn struct {
    // ... 现有字段 ...
    
    // 连接状态跟踪
    connectedAt    time.Time     // 连接建立时间
    lastPingAt     time.Time     // 最后一次成功ping时间
    lastPongAt     time.Time     // 最后一次收到pong时间
    closeReason    string        // 关闭原因
    closeInitiator string        // 关闭发起方
}
```

### 2. 完善错误日志

#### 改进前的ping错误日志：
```
log.Errorf("发送ping消息失败，设备ID: %s, 错误: %v", deviceID, err)
```

#### 改进后的ping错误日志：
```
log.Errorf("发送ping消息失败，设备ID: %s, 错误类型: %s, 错误详情: %v, 连接持续时间: %v, 距离上次pong: %v, 关闭发起方: %s", 
    deviceID, errorType, err, connectionDuration, timeSinceLastPong, instance.closeInitiator)
```

### 3. 错误类型分析

系统现在能够识别不同类型的错误：

- `connection_closed_by_local`: 本地已发送关闭帧（"close sent"错误）
- `network_connection_closed`: 网络连接已关闭
- `websocket_close_error`: WebSocket协议层关闭错误

### 4. 关闭发起方识别

系统能够识别连接关闭的发起方：

- `client`: 客户端主动关闭连接
- `server`: 服务端主动关闭连接  
- `network`: 网络层关闭连接
- `unknown`: 无法确定发起方

### 5. 连接生命周期跟踪

- **连接建立时间**: 记录连接创建的时间戳
- **心跳信息**: 跟踪ping/pong的时间，帮助分析网络状况
- **连接持续时间**: 在关闭时计算连接的总持续时间

## 新的日志输出示例

### 场景1：客户端主动关闭后的ping错误
```
[error] 发送ping消息失败，设备ID: 1a:78:30:64:16:a9, 错误类型: connection_closed_by_local, 错误详情: websocket: close sent, 连接持续时间: 5m30s, 距离上次pong: 2m15s, 关闭发起方: client
```

### 场景2：网络异常导致的连接关闭
```
[debug] WebSocket连接关闭，设备ID: 1a:78:30:64:16:a9, 发起方: network, 原因: read tcp: connection reset by peer, 连接持续时间: 3m45s
```

### 场景3：正常的客户端关闭
```
[info] 收到客户端关闭消息，设备ID: 1a:78:30:64:16:a9, 关闭码: 1000, 消息: normal closure, 连接持续时间: 10m20s
```

### 场景4：服务端主动关闭
```
[info] 主动关闭WebSocket连接，设备ID: 1a:78:30:64:16:a9, 发起方: server, 原因: server initiated close, 连接持续时间: 8m15s
```

## 问题分析指南

### "websocket: close sent"错误的含义

这个错误表示：
1. 连接已经发送了关闭帧
2. 尝试在已关闭的连接上发送ping消息
3. 通常发生在连接关闭和心跳检测的竞态条件中

### 诊断步骤

1. **查看关闭发起方**: 确定是客户端、服务端还是网络问题
2. **检查连接持续时间**: 判断是否为超时关闭
3. **分析pong响应时间**: 评估网络状况
4. **查看关闭原因**: 了解具体的关闭触发条件

### 常见情况分析

1. **客户端正常关闭**: 关闭发起方为"client"，关闭码为1000
2. **网络异常**: 关闭发起方为"network"，错误信息包含网络相关描述
3. **超时关闭**: 距离上次pong时间较长，可能是网络延迟问题
4. **服务端主动关闭**: 关闭发起方为"server"，通常是业务逻辑触发

## 性能影响

- 新增的状态跟踪字段占用内存很少（约40字节/连接）
- 日志记录不会显著影响性能
- 错误分析只在异常情况下执行，对正常流程无影响

## 测试验证

已添加完整的单元测试，覆盖以下场景：
- 客户端主动关闭连接
- 服务端主动关闭连接  
- 网络异常导致的连接关闭
- 连接生命周期跟踪

测试结果显示所有功能正常工作，能够正确识别关闭发起方和记录详细的错误信息。