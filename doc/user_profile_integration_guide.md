# GetUserProfile 最佳调用时机指南

## 问题背景

在启用长记忆功能时，`GetUserProfile` 方法可以获取用户的个性化画像信息，为LLM提供更丰富的上下文。本文档分析了在xiaozhi-backend-server中调用此方法的最佳时机。

## 核心调用位置

### 🎯 **推荐方案：在LLM请求组装阶段调用**

**位置**: `internal/app/server/chat/llm.go` 的 `GetMessages` 方法

```go
func (l *LLMManager) GetMessages(ctx context.Context, userMessage *schema.Message, count int) []*schema.Message {
    // 获取历史消息
    messageList := l.clientState.GetMessages(count)
    
    // 构建系统提示词，如果启用长记忆则包含用户画像
    systemPrompt := l.clientState.SystemPrompt
    
    // 尝试获取用户画像（如果启用了长记忆）
    memory := llm_memory.Get()
    if memory != nil && memory.GetMemoryType() != "none" {
        userProfile, err := memory.GetUserProfile(ctx, l.clientState.DeviceID)
        if err != nil {
            log.Warnf("获取用户画像失败: %v", err)
        } else if userProfile != "" {
            // 将用户画像融入系统提示词
            systemPrompt = fmt.Sprintf("%s\n\n# 用户画像\n%s", systemPrompt, userProfile)
        }
    }
    
    // 组装最终消息
    retMessage := make([]*schema.Message, 0)
    retMessage = append(retMessage, &schema.Message{
        Role:    schema.System,
        Content: systemPrompt,
    })
    retMessage = append(retMessage, messageList...)
    if userMessage != nil {
        retMessage = append(retMessage, userMessage)
    }
    return retMessage
}
```

### ✅ **优势分析**

1. **时机精准**: 每次LLM请求前都获取最新的用户画像
2. **性能合理**: 只在需要时调用，避免不必要的开销
3. **上下文完整**: 与系统提示词结合，为LLM提供丰富背景
4. **架构清晰**: 符合数据流向，不破坏现有设计

### 📊 **调用流程图**

```
用户输入 -> DoLLmRequest -> GetMessages -> GetUserProfile -> 组装系统提示词 -> LLM推理
```

## 辅助调用位置

### 🔄 **混合记忆层面的集成**

**位置**: `internal/domain/llm/memory/hybrid/hybrid.go` 的 `GetMessagesForLLM` 方法

```go
func (h *HybridMemory) GetMessagesForLLM(ctx context.Context, deviceID string, count int) ([]*schema.Message, error) {
    var allMessages []*schema.Message

    // 获取长记忆上下文和用户画像
    if h.longTerm != nil && h.longTerm.IsEnabled() {
        // 获取长记忆消息
        longMessages, err := h.longTerm.GetMessagesForLLM(ctx, deviceID, 0)
        if err == nil {
            allMessages = append(allMessages, longMessages...)
        }
        
        // 获取用户画像作为上下文信息
        userProfile, err := h.longTerm.GetUserProfile(ctx, deviceID)
        if err == nil && userProfile != "" {
            profileMessage := &schema.Message{
                Role:    schema.System,
                Content: fmt.Sprintf("# 用户背景信息\n%s", userProfile),
            }
            allMessages = append([]*schema.Message{profileMessage}, allMessages...)
        }
    }

    // 获取短记忆的最近对话
    if h.shortTerm != nil && h.shortTerm.IsEnabled() {
        shortMessages, err := h.shortTerm.GetMessagesForLLM(ctx, deviceID, count)
        if err == nil {
            allMessages = append(allMessages, shortMessages...)
        }
    }

    return allMessages, nil
}
```

### 💡 **预加载方案（可选）**

**位置**: `internal/app/server/chat/chat.go` 的会话初始化

```go
// 在会话初始化时预获取用户画像（作为缓存）
if localMemory.GetMemoryType() != "none" {
    userProfile, err := localMemory.GetUserProfile(ctx, deviceID)
    if err == nil && userProfile != "" {
        log.Infof("为设备 %s 在会话初始化时获取到用户画像", deviceID)
        // 可以存储在clientState中作为缓存
    }
}
```

## 调用时机对比

| 调用位置 | 优势 | 劣势 | 适用场景 |
|---------|------|------|---------|
| **GetMessages方法** ✅ | • 时机精准<br>• 数据最新<br>• 架构清晰 | • 每次请求都调用 | **推荐主方案** |
| 混合记忆层 | • 记忆系统内部集成<br>• 逻辑集中 | • 可能与主系统重复<br>• 架构复杂度增加 | 记忆系统内部使用 |
| 会话初始化 | • 减少重复调用<br>• 提前准备 | • 数据可能过时<br>• 缓存管理复杂 | 长会话场景的优化 |

## 实现注意事项

### 🛡️ **错误处理**

```go
userProfile, err := memory.GetUserProfile(ctx, deviceID)
if err != nil {
    log.Warnf("获取用户画像失败: %v", err)
    // 继续执行，不因为用户画像失败而中断LLM请求
} else if userProfile != "" {
    // 只有在用户画像非空时才融入系统提示词
    systemPrompt = fmt.Sprintf("%s\n\n# 用户画像\n%s", systemPrompt, userProfile)
}
```

### 🔍 **调试和监控**

```go
if userProfile != "" {
    log.Debugf("为设备 %s 获取到用户画像，长度: %d 字符", deviceID, len(userProfile))
    // 可以添加更多监控指标
}
```

### ⚡ **性能优化**

1. **缓存策略**: 在短时间内可以缓存用户画像，避免频繁调用
2. **异步获取**: 对于非关键路径，可以考虑异步获取
3. **超时控制**: 设置合理的超时时间，避免阻塞LLM请求

## 配置示例

### 启用长记忆的配置

```json
{
  "memory": {
    "type": "hybrid",
    "enabled": true,
    "long_term": {
      "provider": "memobase",
      "enabled": true,
      "config": {
        "project_url": "your_memobase_url",
        "api_key": "your_api_key"
      }
    },
    "short_term": {
      "enabled": true,
      "max_messages": 50,
      "ttl": 3600
    }
  }
}
```

## 效果验证

### 预期效果

启用用户画像后，LLM将收到类似如下的系统提示词：

```
你是一个智能助手。

# 用户画像
用户是一个技术爱好者，经常询问AI和编程相关问题，偏好详细的技术解释。用户对深度学习特别感兴趣，曾多次询问关于神经网络架构的问题。
```

### 测试方法

1. **单元测试**: 验证GetMessages方法正确集成用户画像
2. **集成测试**: 测试完整的对话流程
3. **日志监控**: 观察用户画像获取和使用情况

## 最佳实践建议

1. **渐进式启用**: 先在测试环境验证效果，再逐步推广到生产环境
2. **内容过滤**: 对用户画像内容进行适当过滤，避免敏感信息
3. **长度控制**: 控制用户画像长度，避免占用过多token
4. **定期更新**: 确保用户画像反映最新的用户行为和偏好

## 结论

**推荐在 `LLMManager.GetMessages` 方法中调用 `GetUserProfile`**，这是最佳的调用时机，既保证了数据的实时性，又维护了架构的清晰性，为用户提供更个性化的AI交互体验。