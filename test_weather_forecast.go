package main

import (
	"context"
	"fmt"
	"log"

	"xiaozhi-esp32-server-golang/internal/fc_tools"
	"xiaozhi-esp32-server-golang/internal/fc_tools/functions"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
)

// MockConnection implements the types.Connection interface for testing
type MockConnection struct{}

func (m *MockConnection) GetLogger() types.Logger {
	return &MockLogger{}
}

func (m *MockConnection) GetClientIP() string {
	return "127.0.0.1"
}

// MockLogger implements the types.Logger interface for testing
type MockLogger struct{}

func (m *<PERSON>ckLogger) Debug(msg string, args ...interface{}) {
	log.Printf("[DEBUG] "+msg, args...)
}

func (m *<PERSON>ckLogger) Info(msg string, args ...interface{}) {
	log.Printf("[INFO] "+msg, args...)
}

func (m *<PERSON><PERSON><PERSON>ogger) Warn(msg string, args ...interface{}) {
	log.Printf("[WARN] "+msg, args...)
}

func (m *MockLogger) Error(msg string, args ...interface{}) {
	log.Printf("[ERROR] "+msg, args...)
}

func (m *MockLogger) WithTag(tag string) types.Logger {
	return m
}

func main() {
	fmt.Println("Testing Weather Forecast Function...")

	// Create a mock connection
	conn := &MockConnection{}

	// Create FC Tools instance
	fcTools := fc_tools.NewFCTools(conn)

	// Test parameters for weather forecast
	args := map[string]interface{}{
		"location":  "北京",
		"lang":      "zh",
		"days":      "7d",
		"api_host":  "devapi.qweather.com", // You would need to replace with actual API host
		"api_key":   "your_api_key_here",   // You would need to replace with actual API key
	}

	ctx := context.Background()

	// Test the weather forecast function
	fmt.Println("\n=== Testing Weather Forecast Function ===")
	response, err := fcTools.ExecuteFunction(ctx, conn, functions.GetWeatherForecastFunctionName, args)
	if err != nil {
		fmt.Printf("Error executing weather forecast function: %v\n", err)
	} else {
		fmt.Printf("Weather forecast response: %+v\n", response)
	}

	// Test the original weather function for comparison
	fmt.Println("\n=== Testing Original Weather Function ===")
	response2, err := fcTools.ExecuteFunction(ctx, conn, functions.GetWeatherFunctionName, args)
	if err != nil {
		fmt.Printf("Error executing weather function: %v\n", err)
	} else {
		fmt.Printf("Weather response: %+v\n", response2)
	}

	fmt.Println("\nTest completed!")
}
