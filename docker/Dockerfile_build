# =================================================================
# Stage 1: Builder - 编译 Go 应用并准备运行时依赖
# =================================================================
#FROM ubuntu:24.04 AS builder
#
## 设置构建参数，避免构建过程中的交互式提示
#ARG DEBIAN_FRONTEND=noninteractive
#ARG TZ=Asia/Shanghai
#
## 统一更新APT源为适用于Ubuntu 24.04 (noble) 的阿里源，并安装构建依赖
#RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources && \
#    apt-get update && \
#    apt-get install -y --no-install-recommends \
#    curl \
#    build-essential \
#    git \
#    cmake \
#    pkg-config \
#    libogg-dev \
#    libopus-dev \
#    libopusfile-dev \
#    ca-certificates && \
#    rm -rf /var/lib/apt/lists/*
#
## 安装 Miniforge (Mamba)
#RUN curl -L "https://mirror.nju.edu.cn/github-release/conda-forge/miniforge/LatestRelease/Miniforge3-$(uname)-$(uname -m).sh" -o /tmp/miniforge.sh && \
#    chmod +x /tmp/miniforge.sh && \
#    /tmp/miniforge.sh -b -p /opt/conda && \
#    rm /tmp/miniforge.sh
#
## 将 Conda/Mamba 的可执行文件目录添加到 PATH 环境变量
## 这是在 Dockerfile 中正确使用 mamba/conda 的方式
#ENV PATH=/opt/conda/bin:$PATH
#
## 一次性安装 Go 和 ONNX Runtime 以减少层数
#RUN mamba install -y -c conda-forge go onnxruntime c-compiler
#
#COPY docker/lib/onnxruntime-linux-x64-1.21.0.tgz /tmp/
#
#RUN cd /tmp && \
#    tar -xzf onnxruntime-linux-x64-1.21.0.tgz && \
#    mkdir -p /usr/local/include/onnxruntime && \
#    cp -r onnxruntime-linux-x64-1.21.0/include/* /usr/local/include/onnxruntime/ && \
#    cp -r onnxruntime-linux-x64-1.21.0/lib/* /usr/local/lib/ && \
#    rm -rf onnxruntime-linux-x64-1.21.0* && \
#    ldconfig
#
## 设置 Go 代理
#ENV GOPROXY=https://goproxy.cn,direct
#ENV CGO_ENABLED=1
## 设置 CGO 环境变量，让 Go 能找到 ONNX Runtime 头文件和库
#ENV CGO_CFLAGS="-I/usr/local/include/onnxruntime"
#ENV CGO_LDFLAGS="-L/usr/local/lib -lonnxruntime"
#
## 设置工作目录
#WORKDIR /app
#
## 优化缓存：先复制并下载Go模块依赖
## 只有当 go.mod/go.sum 文件改变时，才会重新执行 go mod download
#COPY go.mod go.sum ./
#RUN go mod download && go version
#
## 复制所有源代码并编译
#COPY . .
#
## 调试：检查 ogg 头文件是否存在
#RUN find /usr -name "ogg.h" -type f 2>/dev/null || echo "ogg.h not found"
#RUN ls -la /usr/include/ogg/ || echo "ogg directory not found"
#RUN dpkg -l | grep ogg || echo "ogg packages not installed"
#
#RUN go build -o /app/xiaozhi_server /app/cmd/server/

FROM hackers365/xiaozhi_golang_build:0.1 AS builder

WORKDIR /app

RUN wget https://mirrors.aliyun.com/golang/go1.25.0.linux-amd64.tar.gz && rm -rf /usr/local/go && tar -C /usr/local -xzf go1.25.0.linux-amd64.tar.gz

## 只有当 go.mod/go.sum 文件改变时，才会重新执行 go mod download
COPY go.mod go.sum ./
RUN go mod download && go version

# 复制所有源代码并编译
COPY . .

RUN go build -tags sherpa_onnx -ldflags="-s -w" -o /app/xiaozhi_server /app/cmd/server/

# =================================================================
# Stage 2: Final Image - 创建轻量级的生产镜像
# =================================================================
#FROM ubuntu:24.04-slim
FROM ubuntu:24.04

# 设置运行时环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装必要的运行时依赖，并清理APT缓存
# 注意：这里只需要运行时库(libopus0)，不需要开发包(libopus-dev)
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    libopus0 \
    libopusfile0 \
    libogg0 \
    ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /workspace

# 从 builder 阶段复制 ONNX Runtime 及其依赖的动态链接库
# 并更新动态链接库缓存
COPY --from=builder /usr/local/lib/libonnxruntime.so* /usr/local/lib/

#RUN ldconfig && \
#    mkdir -p /workspace/config/models/tts && \
#    cd /workspace/config/models/tts && \
#    curl -SL -O https://github.com/k2-fsa/sherpa-onnx/releases/download/tts-models/kokoro-multi-lang-v1_0.tar.bz2 && \
#    tar xf kokoro-multi-lang-v1_0.tar.bz2 && \
#    rm kokoro-multi-lang-v1_0.tar.bz2

# 从 builder 阶段仅复制编译好的二进制文件
COPY --from=builder /app/xiaozhi_server /workspace/bin/xiaozhi_server

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    curl https://github.com/k2-fsa/sherpa-onnx-go-linux/raw/refs/heads/master/lib/x86_64-unknown-linux-gnu/libsherpa-onnx-cxx-api.so > /usr/local/lib/libsherpa-onnx-cxx-api.so && \
    curl https://github.com/k2-fsa/sherpa-onnx-go-linux/raw/refs/heads/master/lib/x86_64-unknown-linux-gnu/libsherpa-onnx-c-api.so > /usr/local/lib/libsherpa-onnx-c-api.so && \
    ldconfig && \
    mkdir -p  /workspace/logs && \
    echo "install ok"

# 创建运行时需要的目录和文件
COPY config/ /workspace/config

# 暴露端口
EXPOSE 8989 8990 1883 2883 8883

# 启动命令
CMD ["/workspace/bin/xiaozhi_server", "-c", "/workspace/config/config.yaml"]
