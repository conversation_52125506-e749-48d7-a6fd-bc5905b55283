FROM registry.cn-shanghai.aliyuncs.com/dapp/xiaozhi:ubuntu-runtime-base AS builder

WORKDIR /app

ENV GOVERSION=1.25.0

RUN apt-get update && apt-get install -y curl libopus-dev libopusfile-dev pkg-config build-essential

RUN wget -q -O go.tgz https://mirrors.aliyun.com/golang/go${GOVERSION}.linux-amd64.tar.gz && rm -rf /usr/local/go && tar -C /usr/local -xzf go.tgz
# 设置 Go 代理
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=1
# 设置 CGO 环境变量，让 Go 能找到 ONNX Runtime 头文件和库
ENV CGO_CFLAGS="-I/usr/local/include/onnxruntime"
ENV CGO_LDFLAGS="-L/usr/local/lib -lonnxruntime"
ENV PATH=/usr/local/go/bin:$PATH


## 只有当 go.mod/go.sum 文件改变时，才会重新执行 go mod download
COPY go.mod go.sum ./
RUN go mod download && go version

# 复制所有源代码并编译
COPY . .

ARG VERSION=`v0.1`
ENV VERSION=${VERSION}
#RUN go build -tags sherpa_onnx -ldflags="-s -w" -o /app/xiaozhi_server /app/cmd/server/
RUN go build -ldflags="-s -w -X 'main.version=${VERSION}'" -o /app/xiaozhi_server /app/cmd/server/

# =================================================================
# Stage 2: Final Image - 创建轻量级的生产镜像
# =================================================================
#FROM ubuntu:24.04-slim
FROM registry.cn-shanghai.aliyuncs.com/dapp/xiaozhi:ubuntu-runtime-base

# 设置工作目录
WORKDIR /workspace

# 从 builder 阶段仅复制编译好的二进制文件
COPY --from=builder /app/xiaozhi_server /workspace/bin/xiaozhi_server

# 创建运行时需要的目录和文件
COPY config/ /workspace/config

# 暴露端口
EXPOSE 8989 8990 1883 2883 8883

# 启动命令
CMD ["/workspace/bin/xiaozhi_server", "-c", "/workspace/config/config.yaml"]
