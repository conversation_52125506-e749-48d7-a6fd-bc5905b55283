FROM ubuntu:24.04

# 设置运行时环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 设置 ONNX Runtime 的版本号，方便后续修改
ARG ONNXRUNTIME_VERSION=1.21.0
# 定义其他变量，提高可读性和可维护性
ENV ONNXRUNTIME_PACKAGE=onnxruntime-linux-x64-${ONNXRUNTIME_VERSION}
ENV ONNXRUNTIME_TGZ=${ONNXRUNTIME_PACKAGE}.tgz

# 安装必要的运行时依赖，并清理APT缓存
# 注意：这里只需要运行时库(libopus0)，不需要开发包(libopus-dev)
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo ${TZ} > /etc/timezone && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    wget tar \
    libopus0 \
    libopusfile0 \
    libogg0 \
    ca-certificates && \
    wget https://github.com/microsoft/onnxruntime/releases/download/v${ONNXRUNTIME_VERSION}/${ONNXRUNTIME_TGZ} && \
    tar -zxvf ${ONNXRUNTIME_TGZ} && \
    cp -R ${ONNXRUNTIME_PACKAGE}/lib/* /usr/local/lib/ && \
    cp -R ${ONNXRUNTIME_PACKAGE}/include/* /usr/local/include/ && \
    rm ${ONNXRUNTIME_TGZ} && \
    rm -rf ${ONNXRUNTIME_PACKAGE} && \
    echo "install onnxruntime ok..." && \
    wget -O /usr/local/lib/libsherpa-onnx-cxx-api.so https://github.com/k2-fsa/sherpa-onnx-go-linux/raw/refs/heads/master/lib/x86_64-unknown-linux-gnu/libsherpa-onnx-cxx-api.so && \
    wget -O /usr/local/lib/libsherpa-onnx-c-api.so https://github.com/k2-fsa/sherpa-onnx-go-linux/raw/refs/heads/master/lib/x86_64-unknown-linux-gnu/libsherpa-onnx-c-api.so && \
    chmod +x /usr/local/lib/*.so && \
    echo "install sherpa onnx ok..." && \
    ldconfig && \
    rm -rf /var/lib/apt/lists/*
