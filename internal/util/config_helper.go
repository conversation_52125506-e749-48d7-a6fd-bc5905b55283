package util

import (
	"fmt"
	"reflect"
	"strconv"
	"time"
)

// ConfigHelper 配置获取助手，提供类型安全的配置值获取
type ConfigHelper struct {
	config map[string]interface{}
}

// NewConfigHelper 创建新的配置助手
func NewConfigHelper(config map[string]interface{}) *ConfigHelper {
	return &ConfigHelper{config: config}
}

// GetString 获取字符串值
func (c *ConfigHelper) GetString(key string, defaultValue ...string) string {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultString(defaultValue)
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%g", v)
	case bool:
		return fmt.Sprintf("%t", v)
	default:
		return c.getDefaultString(defaultValue)
	}
}

// GetInt 获取整数值
func (c *ConfigHelper) GetInt(key string, defaultValue ...int) int {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultInt(defaultValue)
	}

	switch v := value.(type) {
	case int:
		return v
	case int8:
		return int(v)
	case int16:
		return int(v)
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint:
		return int(v)
	case uint8:
		return int(v)
	case uint16:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	case string:
		if intVal, err := strconv.Atoi(v); err == nil {
			return intVal
		}
	}
	return c.getDefaultInt(defaultValue)
}

// GetFloat64 获取浮点数值
func (c *ConfigHelper) GetFloat64(key string, defaultValue ...float64) float64 {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultFloat64(defaultValue)
	}

	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int, int8, int16, int32, int64:
		return float64(reflect.ValueOf(v).Int())
	case uint, uint8, uint16, uint32, uint64:
		return float64(reflect.ValueOf(v).Uint())
	case string:
		if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
			return floatVal
		}
	}
	return c.getDefaultFloat64(defaultValue)
}

// GetBool 获取布尔值
func (c *ConfigHelper) GetBool(key string, defaultValue ...bool) bool {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultBool(defaultValue)
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		if boolVal, err := strconv.ParseBool(v); err == nil {
			return boolVal
		}
	case int, int8, int16, int32, int64:
		return reflect.ValueOf(v).Int() != 0
	case uint, uint8, uint16, uint32, uint64:
		return reflect.ValueOf(v).Uint() != 0
	case float32, float64:
		return reflect.ValueOf(v).Float() != 0
	}
	return c.getDefaultBool(defaultValue)
}

// GetDuration 获取时间间隔值（支持秒、毫秒等）
func (c *ConfigHelper) GetDuration(key string, unit time.Duration, defaultValue ...time.Duration) time.Duration {
	value := c.GetInt(key, -1)
	if value == -1 {
		return c.getDefaultDuration(defaultValue)
	}
	return time.Duration(value) * unit
}

// GetStringSlice 获取字符串切片
func (c *ConfigHelper) GetStringSlice(key string, defaultValue ...[]string) []string {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultStringSlice(defaultValue)
	}

	switch v := value.(type) {
	case []string:
		return v
	case []interface{}:
		result := make([]string, len(v))
		for i, item := range v {
			result[i] = fmt.Sprintf("%v", item)
		}
		return result
	case string:
		return []string{v}
	}
	return c.getDefaultStringSlice(defaultValue)
}

// GetIntSlice 获取整数切片
func (c *ConfigHelper) GetIntSlice(key string, defaultValue ...[]int) []int {
	value, exists := c.config[key]
	if !exists {
		return c.getDefaultIntSlice(defaultValue)
	}

	switch v := value.(type) {
	case []int:
		return v
	case []interface{}:
		result := make([]int, 0, len(v))
		for _, item := range v {
			helper := NewConfigHelper(map[string]interface{}{"temp": item})
			result = append(result, helper.GetInt("temp"))
		}
		return result
	}
	return c.getDefaultIntSlice(defaultValue)
}

// HasKey 检查是否存在指定的键
func (c *ConfigHelper) HasKey(key string) bool {
	_, exists := c.config[key]
	return exists
}

// GetKeys 获取所有键
func (c *ConfigHelper) GetKeys() []string {
	keys := make([]string, 0, len(c.config))
	for key := range c.config {
		keys = append(keys, key)
	}
	return keys
}

// 私有辅助方法

func (c *ConfigHelper) getDefaultString(defaultValue []string) string {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return ""
}

func (c *ConfigHelper) getDefaultInt(defaultValue []int) int {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return 0
}

func (c *ConfigHelper) getDefaultFloat64(defaultValue []float64) float64 {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return 0.0
}

func (c *ConfigHelper) getDefaultBool(defaultValue []bool) bool {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return false
}

func (c *ConfigHelper) getDefaultDuration(defaultValue []time.Duration) time.Duration {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return 0
}

func (c *ConfigHelper) getDefaultStringSlice(defaultValue [][]string) []string {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return []string{}
}

func (c *ConfigHelper) getDefaultIntSlice(defaultValue [][]int) []int {
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return []int{}
}

// ConfigValidator 配置验证器
type ConfigValidator struct {
	errors []error
}

// NewConfigValidator 创建新的配置验证器
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{
		errors: make([]error, 0),
	}
}

// RequireString 要求必须有字符串值
func (v *ConfigValidator) RequireString(helper *ConfigHelper, key string, fieldName string) *ConfigValidator {
	if !helper.HasKey(key) || helper.GetString(key) == "" {
		v.errors = append(v.errors, fmt.Errorf("%s 不能为空", fieldName))
	}
	return v
}

// RequirePositiveInt 要求必须有正整数值
func (v *ConfigValidator) RequirePositiveInt(helper *ConfigHelper, key string, fieldName string) *ConfigValidator {
	if !helper.HasKey(key) || helper.GetInt(key) <= 0 {
		v.errors = append(v.errors, fmt.Errorf("%s 必须是正整数", fieldName))
	}
	return v
}

// RequireNonNegativeInt 要求必须有非负整数值
func (v *ConfigValidator) RequireNonNegativeInt(helper *ConfigHelper, key string, fieldName string) *ConfigValidator {
	if !helper.HasKey(key) || helper.GetInt(key) < 0 {
		v.errors = append(v.errors, fmt.Errorf("%s 必须是非负整数", fieldName))
	}
	return v
}

// ValidateRange 验证数值范围
func (v *ConfigValidator) ValidateRange(helper *ConfigHelper, key string, fieldName string, min, max int) *ConfigValidator {
	value := helper.GetInt(key)
	if value < min || value > max {
		v.errors = append(v.errors, fmt.Errorf("%s 必须在 %d 和 %d 之间", fieldName, min, max))
	}
	return v
}

// GetErrors 获取所有验证错误
func (v *ConfigValidator) GetErrors() []error {
	return v.errors
}

// GetError 获取
func (v *ConfigValidator) GetError() error {
	return fmt.Errorf("配置验证失败: %v", v.errors)
}

// HasErrors 是否有验证错误
func (v *ConfigValidator) HasErrors() bool {
	return len(v.errors) > 0
}

// FirstError 获取第一个验证错误
func (v *ConfigValidator) FirstError() error {
	if len(v.errors) > 0 {
		return v.errors[0]
	}
	return nil
}
