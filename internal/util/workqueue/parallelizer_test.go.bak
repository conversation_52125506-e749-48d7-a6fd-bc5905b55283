package workqueue

import (
	"context"
	"fmt"
	"testing"
	"time"
)

func TestNewParalle(t *testing.T) {
	idList := []int{}
	for i := 0; i <= 100000000; i++ {
		idList = append(idList, i)
	}

	startTime := time.Now().UnixNano()
	ParallelizeUntilOptimize(context.Background(), 8, len(idList), func(piece int) {
		sum := idList[piece] + 10
		_ = sum
	})
	optimCost := time.Now().UnixNano() - startTime

	fmt.Println(optimCost)

	startTime = time.Now().UnixNano()
	ParallelizeUntil(context.Background(), 8, len(idList), func(piece int) {
		sum := idList[piece] + 10
		_ = sum
	})
	cost := time.Now().UnixNano() - startTime

	fmt.Println(cost)

	fmt.Println(cost / optimCost)
}
