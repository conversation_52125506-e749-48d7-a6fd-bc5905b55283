package types

import (
	"context"

	"github.com/cloudwego/eino/schema"
)

// Connection represents the connection context passed to function tools
// This interface abstracts the connection object from the Python implementation
type Connection interface {
	// GetConfig returns the configuration map
	GetConfig() map[string]interface{}

	// GetClientIP returns the client IP address
	GetClientIP() string

	// GetLoop returns the event loop (for async operations)
	GetLoop() interface{}

	// GetLogger returns the logger instance
	GetLogger() Logger

	// GetDialogue returns the dialogue context
	GetDialogue() Dialogue

	// GetPrompt returns the current prompt
	GetPrompt() string

	// SetPrompt sets the current prompt
	SetPrompt(prompt string)

	// ChangeSystemPrompt changes the system prompt
	ChangeSystemPrompt(prompt string) error

	// SetCloseAfterChat sets whether to close after chat
	SetCloseAfterChat(close bool)

	// GetLastNewsLink returns the last news link (for news functions)
	GetLastNewsLink() map[string]interface{}

	// SetLastNewsLink sets the last news link
	SetLastNewsLink(link map[string]interface{})
}

// Logger represents a logger interface
type Logger interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	WithTag(tag string) Logger
}

// Dialogue represents the dialogue context
type Dialogue interface {
	UpdateSystemMessage(message string) error
}

// FunctionTool represents a function tool that can be called by LLM
type FunctionTool interface {
	// GetInfo returns the tool information for LLM (eino schema)
	GetInfo() *schema.ToolInfo

	// Execute runs the function with given arguments
	Execute(ctx context.Context, conn Connection, args map[string]interface{}) (*ActionResponse, error)

	// GetType returns the tool type
	GetType() ToolType

	// GetName returns the function name
	GetName() string

	// GetDescription returns the function description
	GetDescription() interface{}
}

// FunctionItem represents a registered function item (equivalent to Python's FunctionItem)
type FunctionItem struct {
	Name        string       `json:"name"`
	Description interface{}  `json:"description"`
	Function    FunctionTool `json:"-"` // Don't serialize the function itself
	Type        ToolType     `json:"type"`
}

// NewFunctionItem creates a new FunctionItem
func NewFunctionItem(name string, description interface{}, function FunctionTool, toolType ToolType) *FunctionItem {
	return &FunctionItem{
		Name:        name,
		Description: description,
		Function:    function,
		Type:        toolType,
	}
}

// GetToolInfo returns the eino ToolInfo for this function
func (fi *FunctionItem) GetToolInfo() *schema.ToolInfo {
	if fi.Function != nil {
		return fi.Function.GetInfo()
	}
	return nil
}
