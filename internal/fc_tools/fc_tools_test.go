package fc_tools

import (
	"context"
	"testing"

	"github.com/cloudwego/eino/schema"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
)

// MockConnection implements the Connection interface for testing
type MockConnection struct {
	config           map[string]interface{}
	clientIP         string
	prompt           string
	closeAfterChat   bool
	lastNewsLink     map[string]interface{}
	systemPrompt     string
}

func NewMockConnection() *MockConnection {
	return &MockConnection{
		config:       make(map[string]interface{}),
		lastNewsLink: make(map[string]interface{}),
	}
}

func (m *MockConnection) GetConfig() map[string]interface{} {
	return m.config
}

func (m *MockConnection) GetClientIP() string {
	return m.clientIP
}

func (m *MockConnection) GetLoop() interface{} {
	return nil
}

func (m *MockConnection) GetLogger() types.Logger {
	return &MockLogger{}
}

func (m *MockConnection) GetDialogue() types.Dialogue {
	return &MockDialogue{}
}

func (m *MockConnection) GetPrompt() string {
	return m.prompt
}

func (m *MockConnection) SetPrompt(prompt string) {
	m.prompt = prompt
}

func (m *MockConnection) ChangeSystemPrompt(prompt string) error {
	m.systemPrompt = prompt
	return nil
}

func (m *MockConnection) SetCloseAfterChat(close bool) {
	m.closeAfterChat = close
}

func (m *MockConnection) GetLastNewsLink() map[string]interface{} {
	return m.lastNewsLink
}

func (m *MockConnection) SetLastNewsLink(link map[string]interface{}) {
	m.lastNewsLink = link
}

// MockLogger implements the Logger interface for testing
type MockLogger struct{}

func (m *MockLogger) Debug(msg string, args ...interface{}) {}
func (m *MockLogger) Info(msg string, args ...interface{})  {}
func (m *MockLogger) Warn(msg string, args ...interface{})  {}
func (m *MockLogger) Error(msg string, args ...interface{}) {}
func (m *MockLogger) WithTag(tag string) types.Logger       { return m }

// MockDialogue implements the Dialogue interface for testing
type MockDialogue struct{}

func (m *MockDialogue) UpdateSystemMessage(message string) error {
	return nil
}

// TestFCToolsBasicFunctionality tests basic FCTools functionality
func TestFCToolsBasicFunctionality(t *testing.T) {
	conn := NewMockConnection()
	fcTools := NewFCTools(conn)
	
	// Test that FCTools is created properly
	assert.NotNil(t, fcTools)
	assert.NotNil(t, fcTools.registry)
	assert.NotNil(t, fcTools.deviceRegistry)
	assert.NotNil(t, fcTools.adapterManager)
	assert.NotNil(t, fcTools.converter)
}

// TestFunctionRegistration tests function registration and retrieval
func TestFunctionRegistration(t *testing.T) {
	conn := NewMockConnection()
	fcTools := NewFCTools(conn)
	
	// Create a mock function tool
	mockTool := &MockFunctionTool{
		name:        "test_function",
		description: "Test function description",
		toolType:    types.ToolTypeWait,
	}
	
	// Test registration
	err := fcTools.RegisterFunction("test_function", mockTool)
	require.NoError(t, err)
	
	// Test retrieval
	retrievedTool, err := fcTools.GetFunction("test_function")
	require.NoError(t, err)
	assert.Equal(t, mockTool, retrievedTool)
	
	// Test getting all functions
	allFunctions := fcTools.GetAllFunctions()
	assert.Contains(t, allFunctions, "test_function")
	
	// Test unregistration
	err = fcTools.UnregisterFunction("test_function")
	require.NoError(t, err)
	
	// Verify function is removed
	_, err = fcTools.GetFunction("test_function")
	assert.Error(t, err)
}

// TestFunctionExecution tests function execution
func TestFunctionExecution(t *testing.T) {
	conn := NewMockConnection()
	fcTools := NewFCTools(conn)
	
	// Create a mock function tool
	mockTool := &MockFunctionTool{
		name:        "test_function",
		description: "Test function description",
		toolType:    types.ToolTypeWait,
		executeFunc: func(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
			return types.NewActionResponse(types.ActionDirectResponse, "success", "Test executed"), nil
		},
	}
	
	// Register the function
	err := fcTools.RegisterFunction("test_function", mockTool)
	require.NoError(t, err)
	
	// Execute the function
	ctx := context.Background()
	args := map[string]interface{}{"test_param": "test_value"}
	
	response, err := fcTools.ExecuteFunction(ctx, conn, "test_function", args)
	require.NoError(t, err)
	assert.Equal(t, types.ActionDirectResponse, response.Action)
	assert.Equal(t, "success", response.Result)
	assert.Equal(t, "Test executed", response.Response)
}

// TestEinoIntegration tests eino integration
func TestEinoIntegration(t *testing.T) {
	conn := NewMockConnection()
	fcTools := NewFCTools(conn)
	
	// Create a mock function tool
	mockTool := &MockFunctionTool{
		name:        "test_function",
		description: "Test function description",
		toolType:    types.ToolTypeWait,
	}
	
	// Register the function
	err := fcTools.RegisterFunction("test_function", mockTool)
	require.NoError(t, err)
	
	// Get eino tools
	einoTools := fcTools.GetEinoTools()
	assert.Len(t, einoTools, 1)
	
	// Verify tool info
	toolInfo := einoTools[0]
	assert.Equal(t, "test_function", toolInfo.Name)
}

// MockFunctionTool implements FunctionTool interface for testing
type MockFunctionTool struct {
	name        string
	description interface{}
	toolType    types.ToolType
	executeFunc func(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error)
}

func (m *MockFunctionTool) GetInfo() *schema.ToolInfo {
	return &schema.ToolInfo{
		Name:        m.name,
		Desc:        m.description.(string),
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{}),
	}
}

func (m *MockFunctionTool) Execute(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
	if m.executeFunc != nil {
		return m.executeFunc(ctx, conn, args)
	}
	return types.NewActionResponse(types.ActionDirectResponse, "mock executed", nil), nil
}

func (m *MockFunctionTool) GetType() types.ToolType {
	return m.toolType
}

func (m *MockFunctionTool) GetName() string {
	return m.name
}

func (m *MockFunctionTool) GetDescription() interface{} {
	return m.description
}
