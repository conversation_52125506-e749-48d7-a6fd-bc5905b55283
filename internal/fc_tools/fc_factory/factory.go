package fc_factory

import (
	"encoding/json"
	"xiaozhi-esp32-server-golang/internal/fc_tools"
	"xiaozhi-esp32-server-golang/internal/fc_tools/functions"
	_ "xiaozhi-esp32-server-golang/internal/fc_tools/functions"
)

func GetFCTools(plugins map[string]string) *fc_tools.FCTools {
	ft := fc_tools.NewFCTools(nil)
	for name, config := range plugins {
		var args map[string]interface{}
		err := json.Unmarshal([]byte(config), &args)
		if err != nil {
			continue
		}
		switch name {
		case functions.GetNewsFromChinaNewsFunctionName:
			ft.RegisterFunction(functions.GetNewsFromChinaNewsFunctionName, functions.NewGetNewsFromChinaNewsFunction(args))
		case functions.GetNewsFromNewsNowFunctionName:
			ft.RegisterFunction(functions.GetNewsFromNewsNowFunctionName, functions.NewGetNewsFromNewsNowFunction(args))
		case functions.GetTimeFunctionName:
			ft.RegisterFunction(functions.GetTimeFunctionName, functions.NewGetTimeFunction(args))
		case functions.GetWeatherFunctionName:
			ft.RegisterFunction(functions.GetWeatherFunctionName, functions.NewGetWeatherFunction(args))
		case functions.GetWeatherForecastFunctionName:
			ft.RegisterFunction(functions.GetWeatherForecastFunctionName, functions.NewGetWeatherForecastFunction(args))
		}
	}

	return ft
}
