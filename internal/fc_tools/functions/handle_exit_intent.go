package functions

import (
	"context"
	"xiaozhi-esp32-server-golang/internal/fc_tools/eino_integration"

	"xiaozhi-esp32-server-golang/internal/fc_tools"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
	log "xiaozhi-esp32-server-golang/logger"

	"github.com/cloudwego/eino/schema"
)

const (
	HandleExitIntentFunctionName = "handle_exit_intent"
)

// HandleExitIntentFunctionDesc defines the function description for LLM
var HandleExitIntentFunctionDesc = map[string]interface{}{
	"type": "function",
	"function": map[string]interface{}{
		"name":        HandleExitIntentFunctionName,
		"description": "当用户想结束对话或需要退出系统时调用",
		"parameters": map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"say_goodbye": map[string]interface{}{
					"type":        "string",
					"description": "和用户友好结束对话的告别语",
				},
			},
			"required": []string{"say_goodbye"},
		},
	},
}

// HandleExitIntentFunction implements the exit intent handling function
type HandleExitIntentFunction struct{}

// NewHandleExitIntentFunction creates a new handle exit intent function
func NewHandleExitIntentFunction() *HandleExitIntentFunction {
	return &HandleExitIntentFunction{}
}

// Execute implements the function execution
func (f *HandleExitIntentFunction) Execute(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
	// Use connection logger if available, otherwise use default logger
	var logger types.Logger
	if conn != nil {
		logger = conn.GetLogger()
	}

	// Extract say_goodbye parameter
	var sayGoodbye string
	if goodbye, ok := args["say_goodbye"].(string); ok {
		sayGoodbye = goodbye
	} else {
		sayGoodbye = "再见，祝您生活愉快！"
	}

	// Set close after chat flag if connection supports it
	if conn != nil {
		conn.SetCloseAfterChat(true)
		if logger != nil {
			logger.Info("退出意图已处理: %s", sayGoodbye)
		}
	}

	return types.NewActionResponse(
		types.ActionDirectResponse,
		"退出意图已处理",
		sayGoodbye,
	), nil
}

// GetInfo returns the eino ToolInfo
func (f *HandleExitIntentFunction) GetInfo() *schema.ToolInfo {
	converter := eino_integration.GetGlobalConverter()
	toolInfo, err := converter.ConvertPythonDescToEinoToolInfo(HandleExitIntentFunctionName, HandleExitIntentFunctionDesc)
	if err != nil {
		log.Errorf("Failed to convert tool info for %s: %v", HandleExitIntentFunctionName, err)
		return nil
	}
	return toolInfo
}

// GetType returns the tool type
func (f *HandleExitIntentFunction) GetType() types.ToolType {
	return types.ToolTypeSystemCtl
}

// GetName returns the function name
func (f *HandleExitIntentFunction) GetName() string {
	return HandleExitIntentFunctionName
}

// GetDescription returns the function description
func (f *HandleExitIntentFunction) GetDescription() interface{} {
	return HandleExitIntentFunctionDesc
}

// RegisterHandleExitIntentFunction registers the handle exit intent function
func RegisterHandleExitIntentFunction() error {
	function := NewHandleExitIntentFunction()
	return fc_tools.RegisterGlobalFunction(HandleExitIntentFunctionName, function)
}

// init automatically registers the function when the package is imported
func init() {
	if err := RegisterHandleExitIntentFunction(); err != nil {
		log.Errorf("Failed to register %s function: %v", HandleExitIntentFunctionName, err)
	}
}
