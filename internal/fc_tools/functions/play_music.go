package functions

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"xiaozhi-esp32-server-golang/internal/fc_tools"
	"xiaozhi-esp32-server-golang/internal/fc_tools/eino_integration"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
	log "xiaozhi-esp32-server-golang/logger"

	"github.com/cloudwego/eino/schema"
)

const (
	PlayMusicFunctionName = "play_music"
)

// PlayMusicFunctionDesc defines the function description for LLM
var PlayMusicFunctionDesc = map[string]interface{}{
	"type": "function",
	"function": map[string]interface{}{
		"name":        PlayMusicFunctionName,
		"description": "播放音乐文件，支持多种音频格式，可以播放本地文件或网络音频",
		"parameters": map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"music_path": map[string]interface{}{
					"type":        "string",
					"description": "音乐文件路径或URL，支持mp3、wav、flac等格式",
				},
				"action": map[string]interface{}{
					"type":        "string",
					"description": "操作类型：play(播放)、pause(暂停)、stop(停止)、resume(继续)、volume(调节音量)",
					"default":     "play",
				},
				"volume": map[string]interface{}{
					"type":        "integer",
					"description": "音量大小，范围0-100",
					"minimum":     0,
					"maximum":     100,
					"default":     50,
				},
				"loop": map[string]interface{}{
					"type":        "boolean",
					"description": "是否循环播放",
					"default":     false,
				},
				"background": map[string]interface{}{
					"type":        "boolean",
					"description": "是否后台播放",
					"default":     true,
				},
			},
			"required": []string{},
		},
	},
}

// MusicPlayer represents a music player instance
type MusicPlayer struct {
	currentFile string
	isPlaying   bool
	isPaused    bool
	volume      int
	process     *os.Process
	logger      types.Logger
}

// PlayMusicFunction implements the music playback function
type PlayMusicFunction struct {
	player *MusicPlayer
}

// NewPlayMusicFunction creates a new music function
func NewPlayMusicFunction() *PlayMusicFunction {
	return &PlayMusicFunction{
		player: &MusicPlayer{
			volume: 50,
		},
	}
}

// Execute implements the function execution
func (f *PlayMusicFunction) Execute(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
	var logger types.Logger
	if conn != nil {
		logger = conn.GetLogger()
		f.player.logger = logger
	}

	// Extract parameters
	musicPath, _ := args["music_path"].(string)
	action, _ := args["action"].(string)
	if action == "" {
		action = "play"
	}

	volume := 50
	if v, ok := args["volume"].(float64); ok {
		volume = int(v)
	} else if v, ok := args["volume"].(int); ok {
		volume = v
	}

	loop := false
	if l, ok := args["loop"].(bool); ok {
		loop = l
	}

	background := true
	if bg, ok := args["background"].(bool); ok {
		background = bg
	}

	if logger != nil {
		logger.Info("音乐播放请求: 路径=%s, 动作=%s, 音量=%d", musicPath, action, volume)
	}

	// Handle different actions
	switch action {
	case "play":
		return f.handlePlay(ctx, musicPath, volume, loop, background)
	case "pause":
		return f.handlePause()
	case "stop":
		return f.handleStop()
	case "resume":
		return f.handleResume()
	case "volume":
		return f.handleVolume(volume)
	default:
		return types.NewActionResponse(types.ActionDirectResponse,
			fmt.Sprintf("不支持的操作: %s", action), nil), nil
	}
}

// handlePlay handles music playback
func (f *PlayMusicFunction) handlePlay(ctx context.Context, musicPath string, volume int, loop, background bool) (*types.ActionResponse, error) {
	if musicPath == "" {
		return types.NewActionResponse(types.ActionDirectResponse,
			"请提供音乐文件路径", nil), nil
	}

	// Stop current playback if any
	f.handleStop()

	// Validate music file
	if err := f.validateMusicFile(musicPath); err != nil {
		if f.player.logger != nil {
			f.player.logger.Error("音乐文件验证失败: %v", err)
		}
		return types.NewActionResponse(types.ActionDirectResponse,
			fmt.Sprintf("音乐文件无效: %v", err), nil), nil
	}

	// Start playback
	if err := f.startPlayback(ctx, musicPath, volume, loop, background); err != nil {
		if f.player.logger != nil {
			f.player.logger.Error("音乐播放失败: %v", err)
		}
		return types.NewActionResponse(types.ActionDirectResponse,
			fmt.Sprintf("播放失败: %v", err), nil), nil
	}

	f.player.currentFile = musicPath
	f.player.isPlaying = true
	f.player.isPaused = false
	f.player.volume = volume

	response := fmt.Sprintf("🎵 开始播放: %s", filepath.Base(musicPath))
	if loop {
		response += " (循环播放)"
	}
	if background {
		response += " (后台播放)"
	}

	if f.player.logger != nil {
		f.player.logger.Info("音乐播放开始: %s", musicPath)
	}

	return types.NewActionResponse(types.ActionDirectResponse, response, nil), nil
}

// handlePause handles music pause
func (f *PlayMusicFunction) handlePause() (*types.ActionResponse, error) {
	if !f.player.isPlaying {
		return types.NewActionResponse(types.ActionDirectResponse,
			"当前没有正在播放的音乐", nil), nil
	}

	// In a real implementation, you would send pause signal to the player
	f.player.isPaused = true

	response := "⏸️ 音乐已暂停"
	if f.player.logger != nil {
		f.player.logger.Info("音乐播放暂停")
	}

	return types.NewActionResponse(types.ActionDirectResponse, response, nil), nil
}

// handleStop handles music stop
func (f *PlayMusicFunction) handleStop() (*types.ActionResponse, error) {
	if f.player.process != nil {
		// Kill the music player process
		if err := f.player.process.Kill(); err != nil {
			if f.player.logger != nil {
				f.player.logger.Warn("停止音乐播放进程失败: %v", err)
			}
		}
		f.player.process = nil
	}

	f.player.isPlaying = false
	f.player.isPaused = false
	f.player.currentFile = ""

	response := "⏹️ 音乐播放已停止"
	if f.player.logger != nil {
		f.player.logger.Info("音乐播放停止")
	}

	return types.NewActionResponse(types.ActionDirectResponse, response, nil), nil
}

// handleResume handles music resume
func (f *PlayMusicFunction) handleResume() (*types.ActionResponse, error) {
	if !f.player.isPaused {
		return types.NewActionResponse(types.ActionDirectResponse,
			"当前没有暂停的音乐", nil), nil
	}

	// In a real implementation, you would send resume signal to the player
	f.player.isPaused = false

	response := "▶️ 音乐播放已继续"
	if f.player.logger != nil {
		f.player.logger.Info("音乐播放继续")
	}

	return types.NewActionResponse(types.ActionDirectResponse, response, nil), nil
}

// handleVolume handles volume adjustment
func (f *PlayMusicFunction) handleVolume(volume int) (*types.ActionResponse, error) {
	if volume < 0 || volume > 100 {
		return types.NewActionResponse(types.ActionDirectResponse,
			"音量范围应在0-100之间", nil), nil
	}

	f.player.volume = volume

	response := fmt.Sprintf("🔊 音量已调节至: %d%%", volume)
	if f.player.logger != nil {
		f.player.logger.Info("音量调节至: %d", volume)
	}

	return types.NewActionResponse(types.ActionDirectResponse, response, nil), nil
}

// validateMusicFile validates if the music file exists and is supported
func (f *PlayMusicFunction) validateMusicFile(musicPath string) error {
	// Check if it's a URL
	if strings.HasPrefix(musicPath, "http://") || strings.HasPrefix(musicPath, "https://") {
		// For URLs, we assume they're valid (could add HTTP HEAD request validation)
		return nil
	}

	// Check if local file exists
	if _, err := os.Stat(musicPath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", musicPath)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(musicPath))
	supportedFormats := []string{".mp3", ".wav", ".flac", ".ogg", ".m4a", ".aac"}

	for _, format := range supportedFormats {
		if ext == format {
			return nil
		}
	}

	return fmt.Errorf("不支持的音频格式: %s", ext)
}

// startPlayback starts the actual music playback
func (f *PlayMusicFunction) startPlayback(ctx context.Context, musicPath string, volume int, loop, background bool) error {
	// Try different music players based on the system
	players := f.getAvailablePlayers()

	for _, player := range players {
		cmd := f.buildPlayerCommand(player, musicPath, volume, loop)
		if cmd == nil {
			continue
		}

		if background {
			// Start in background
			if err := cmd.Start(); err != nil {
				continue
			}
			f.player.process = cmd.Process

			// Monitor the process in a goroutine
			go f.monitorPlayback(cmd)

			return nil
		} else {
			// Run and wait
			return cmd.Run()
		}
	}

	return fmt.Errorf("没有找到可用的音乐播放器")
}

// getAvailablePlayers returns a list of available music players
func (f *PlayMusicFunction) getAvailablePlayers() []string {
	// Common music players on different systems
	players := []string{
		"mpg123",  // Linux/Unix
		"ffplay",  // Cross-platform (FFmpeg)
		"mplayer", // Cross-platform
		"vlc",     // Cross-platform
		"afplay",  // macOS
		"paplay",  // Linux (PulseAudio)
	}

	var available []string
	for _, player := range players {
		if _, err := exec.LookPath(player); err == nil {
			available = append(available, player)
		}
	}

	return available
}

// buildPlayerCommand builds the command for a specific player
func (f *PlayMusicFunction) buildPlayerCommand(player, musicPath string, volume int, loop bool) *exec.Cmd {
	switch player {
	case "mpg123":
		args := []string{musicPath}
		if loop {
			args = append([]string{"--loop", "-1"}, args...)
		}
		return exec.Command("mpg123", args...)

	case "ffplay":
		args := []string{"-nodisp", "-autoexit", musicPath}
		if loop {
			args = append([]string{"-loop", "0"}, args...)
		}
		return exec.Command("ffplay", args...)

	case "mplayer":
		args := []string{"-quiet", musicPath}
		if loop {
			args = append([]string{"-loop", "0"}, args...)
		}
		return exec.Command("mplayer", args...)

	case "afplay":
		// macOS built-in player
		return exec.Command("afplay", musicPath)

	case "paplay":
		// Linux PulseAudio
		return exec.Command("paplay", musicPath)

	default:
		return nil
	}
}

// monitorPlayback monitors the playback process
func (f *PlayMusicFunction) monitorPlayback(cmd *exec.Cmd) {
	err := cmd.Wait()

	// Reset player state when playback ends
	f.player.isPlaying = false
	f.player.isPaused = false
	f.player.process = nil

	if err != nil && f.player.logger != nil {
		f.player.logger.Debug("音乐播放进程结束: %v", err)
	}
}

// GetPlayerStatus returns the current player status
func (f *PlayMusicFunction) GetPlayerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_playing":   f.player.isPlaying,
		"is_paused":    f.player.isPaused,
		"current_file": f.player.currentFile,
		"volume":       f.player.volume,
	}
}

// GetInfo returns the eino ToolInfo
func (f *PlayMusicFunction) GetInfo() *schema.ToolInfo {
	converter := eino_integration.GetGlobalConverter()
	toolInfo, err := converter.ConvertPythonDescToEinoToolInfo(PlayMusicFunctionName, PlayMusicFunctionDesc)
	if err != nil {
		log.Errorf("Failed to convert tool info for %s: %v", PlayMusicFunctionName, err)
		return nil
	}
	return toolInfo
}

// GetType returns the tool type
func (f *PlayMusicFunction) GetType() types.ToolType {
	return types.ToolTypeSystemCtl
}

// GetName returns the function name
func (f *PlayMusicFunction) GetName() string {
	return PlayMusicFunctionName
}

// GetDescription returns the function description
func (f *PlayMusicFunction) GetDescription() interface{} {
	return PlayMusicFunctionDesc
}

// RegisterPlayMusicFunction registers the music function
func RegisterPlayMusicFunction() error {
	function := NewPlayMusicFunction()
	return fc_tools.RegisterGlobalFunction(PlayMusicFunctionName, function)
}

// init automatically registers the function when the package is imported
func init() {
	if err := RegisterPlayMusicFunction(); err != nil {
		log.Errorf("Failed to register %s function: %v", PlayMusicFunctionName, err)
	}
}
