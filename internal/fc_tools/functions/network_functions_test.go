package functions

import (
	"context"
	"testing"
	"time"
	
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
	"xiaozhi-esp32-server-golang/internal/fc_tools/utils"
)

// TestGetWeatherFunction tests the weather function
func TestGetWeatherFunction(t *testing.T) {
	function := NewGetWeatherFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	// Set up mock configuration
	conn.config = map[string]interface{}{
		"plugins": map[string]interface{}{
			"get_weather": map[string]interface{}{
				"api_host":         "devapi.qweather.com",
				"api_key":          "test_api_key",
				"default_location": "北京",
			},
		},
	}
	
	t.Run("BasicWeatherQuery", func(t *testing.T) {
		args := map[string]interface{}{
			"location": "北京",
			"lang":     "zh_CN",
		}
		
		// Note: This test will fail with real API calls without valid API key
		// In a real test environment, you would mock the HTTP client
		response, err := function.Execute(ctx, conn, args)
		
		// The function should handle API failures gracefully
		require.NoError(t, err)
		assert.Equal(t, types.ActionReqLLM, response.Action)
		assert.NotNil(t, response.Result)
	})
	
	t.Run("DefaultLocation", func(t *testing.T) {
		args := map[string]interface{}{}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionReqLLM, response.Action)
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, GetWeatherFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeSystemCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "get_weather", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
}

// TestGetNewsFromChinaNewsFunction tests the China News function
func TestGetNewsFromChinaNewsFunction(t *testing.T) {
	function := NewGetNewsFromChinaNewsFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	t.Run("BasicNewsQuery", func(t *testing.T) {
		args := map[string]interface{}{
			"count":    5,
			"category": "all",
		}
		
		// Note: This test may fail with real network calls
		// In a real test environment, you would mock the HTTP client
		response, err := function.Execute(ctx, conn, args)
		
		// The function should handle network failures gracefully
		require.NoError(t, err)
		assert.Equal(t, types.ActionReqLLM, response.Action)
		assert.NotNil(t, response.Result)
	})
	
	t.Run("CategoryMapping", func(t *testing.T) {
		testCases := []struct {
			category string
			expected string
		}{
			{"domestic", "国内"},
			{"international", "国际"},
			{"finance", "财经"},
			{"tech", "科技"},
			{"all", "综合"},
		}
		
		for _, tc := range testCases {
			displayName := function.getCategoryDisplayName(tc.category)
			assert.Equal(t, tc.expected, displayName)
		}
	})
	
	t.Run("CountLimiting", func(t *testing.T) {
		args := map[string]interface{}{
			"count": 20, // Should be limited to 10
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionReqLLM, response.Action)
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, GetNewsFromChinaNewsFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeSystemCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "get_news_from_chinanews", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
}

// TestGetNewsFromNewsNowFunction tests the NewsNow function
func TestGetNewsFromNewsNowFunction(t *testing.T) {
	function := NewGetNewsFromNewsNowFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	t.Run("BasicNewsQuery", func(t *testing.T) {
		args := map[string]interface{}{
			"count":    5,
			"category": "world",
			"region":   "world",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionReqLLM, response.Action)
		assert.NotNil(t, response.Result)
		
		// Check that last news link is set
		lastNewsLink := conn.GetLastNewsLink()
		assert.NotNil(t, lastNewsLink)
		assert.Equal(t, "newsnow", lastNewsLink["source"])
	})
	
	t.Run("CategoryAndRegionMapping", func(t *testing.T) {
		categoryTests := []struct {
			category string
			expected string
		}{
			{"world", "国际"},
			{"business", "商业"},
			{"technology", "科技"},
			{"sports", "体育"},
		}
		
		for _, tc := range categoryTests {
			displayName := function.getCategoryDisplayName(tc.category)
			assert.Equal(t, tc.expected, displayName)
		}
		
		regionTests := []struct {
			region   string
			expected string
		}{
			{"us", "美国"},
			{"uk", "英国"},
			{"eu", "欧盟"},
			{"world", ""},
		}
		
		for _, tc := range regionTests {
			displayName := function.getRegionDisplayName(tc.region)
			assert.Equal(t, tc.expected, displayName)
		}
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, GetNewsFromNewsNowFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeSystemCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "get_news_from_newsnow", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
}

// TestCacheIntegration tests cache functionality with network functions
func TestCacheIntegration(t *testing.T) {
	// Clear cache before testing
	utils.GlobalCacheManager.ClearAll()
	
	t.Run("WeatherCaching", func(t *testing.T) {
		// Test cache miss and set
		cacheKey := "weather_test_beijing_zh_CN"
		
		// Should not exist initially
		_, found := utils.GetCache(utils.CacheTypeWeather, cacheKey)
		assert.False(t, found)
		
		// Set cache
		testData := "Test weather data"
		utils.SetCacheWithDefaultTTL(utils.CacheTypeWeather, cacheKey, testData)
		
		// Should exist now
		cachedData, found := utils.GetCache(utils.CacheTypeWeather, cacheKey)
		assert.True(t, found)
		assert.Equal(t, testData, cachedData)
	})
	
	t.Run("NewsCaching", func(t *testing.T) {
		// Test cache with custom TTL
		cacheKey := "news_test_category_5"
		testData := "Test news data"
		
		utils.SetCache(utils.CacheTypeNews, cacheKey, testData, 1*time.Minute)
		
		// Should exist
		cachedData, found := utils.GetCache(utils.CacheTypeNews, cacheKey)
		assert.True(t, found)
		assert.Equal(t, testData, cachedData)
		
		// Test cache stats
		stats := utils.GlobalCacheManager.GetStats()
		assert.Contains(t, stats, utils.CacheTypeNews)
		assert.Contains(t, stats, utils.CacheTypeWeather)
	})
	
	t.Run("CacheExpiration", func(t *testing.T) {
		// Test with very short TTL
		cacheKey := "test_expiration"
		testData := "Test expiring data"
		
		utils.SetCache(utils.CacheTypeGeneral, cacheKey, testData, 1*time.Millisecond)
		
		// Should exist immediately
		cachedData, found := utils.GetCache(utils.CacheTypeGeneral, cacheKey)
		assert.True(t, found)
		assert.Equal(t, testData, cachedData)
		
		// Wait for expiration
		time.Sleep(10 * time.Millisecond)
		
		// Should not exist after expiration
		_, found = utils.GetCache(utils.CacheTypeGeneral, cacheKey)
		assert.False(t, found)
	})
}

// TestHTTPClientUtils tests the HTTP client utilities
func TestHTTPClientUtils(t *testing.T) {
	t.Run("HTTPClientCreation", func(t *testing.T) {
		config := utils.HTTPClientConfig{
			Timeout: 10 * time.Second,
			Retries: 2,
		}
		
		client := utils.NewHTTPClient(config)
		assert.NotNil(t, client)
		assert.Equal(t, 10*time.Second, client.GetTimeout())
		assert.Equal(t, 2, client.GetRetries())
	})
	
	t.Run("DefaultHTTPClient", func(t *testing.T) {
		assert.NotNil(t, utils.DefaultHTTPClient)
		assert.Equal(t, 30*time.Second, utils.DefaultHTTPClient.GetTimeout())
		assert.Equal(t, 3, utils.DefaultHTTPClient.GetRetries())
	})
	
	t.Run("HTTPClientConfiguration", func(t *testing.T) {
		client := utils.NewHTTPClient(utils.HTTPClientConfig{})
		
		// Test default values
		assert.Equal(t, 30*time.Second, client.GetTimeout())
		assert.Equal(t, 3, client.GetRetries())
		
		// Test setting new values
		client.SetTimeout(5 * time.Second)
		client.SetRetries(1)
		
		assert.Equal(t, 5*time.Second, client.GetTimeout())
		assert.Equal(t, 1, client.GetRetries())
	})
}

// TestUtilityFunctions tests various utility functions
func TestUtilityFunctions(t *testing.T) {
	t.Run("DateParsing", func(t *testing.T) {
		chinaNewsFunc := NewGetNewsFromChinaNewsFunction()

		// Test various date formats
		testDates := []string{
			"Mon, 02 Jan 2006 15:04:05 MST",
			"2006-01-02 15:04:05",
			"2006-01-02T15:04:05Z",
		}

		for _, dateStr := range testDates {
			_, err := chinaNewsFunc.parseChineseDate(dateStr)
			// Some may fail due to timezone issues, but should not panic
			_ = err // Ignore error for this test
		}
	})
	
	t.Run("DescriptionCleaning", func(t *testing.T) {
		newsFunc := NewGetNewsFromChinaNewsFunction()
		
		testCases := []struct {
			input    string
			expected string
		}{
			{"<p>Test content</p>", "Test content"},
			{"<div>Multiple   spaces</div>", "Multiple spaces"},
			{"Normal text", "Normal text"},
		}
		
		for _, tc := range testCases {
			result := newsFunc.cleanDescription(tc.input)
			assert.Equal(t, tc.expected, result)
		}
	})
	
	t.Run("LinkExtraction", func(t *testing.T) {
		newsFunc := NewGetNewsFromNewsNowFunction()
		
		items := []NewsNowItem{
			{URL: "https://example.com/1"},
			{URL: "https://example.com/2"},
			{URL: ""}, // Empty URL should be ignored
		}
		
		links := newsFunc.extractLinks(items)
		assert.Len(t, links, 2)
		assert.Contains(t, links, "https://example.com/1")
		assert.Contains(t, links, "https://example.com/2")
	})
}
