package functions

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
)

// MockConnection implements the Connection interface for testing
type MockConnection struct {
	config           map[string]interface{}
	clientIP         string
	prompt           string
	closeAfterChat   bool
	lastNewsLink     map[string]interface{}
	systemPrompt     string
}

func NewMockConnection() *MockConnection {
	return &MockConnection{
		config:       make(map[string]interface{}),
		lastNewsLink: make(map[string]interface{}),
	}
}

func (m *MockConnection) GetConfig() map[string]interface{} {
	return m.config
}

func (m *MockConnection) GetClientIP() string {
	return m.clientIP
}

func (m *MockConnection) GetLoop() interface{} {
	return nil
}

func (m *MockConnection) GetLogger() types.Logger {
	return &MockLogger{}
}

func (m *MockConnection) GetDialogue() types.Dialogue {
	return &MockDialogue{}
}

func (m *MockConnection) GetPrompt() string {
	return m.prompt
}

func (m *MockConnection) SetPrompt(prompt string) {
	m.prompt = prompt
}

func (m *MockConnection) ChangeSystemPrompt(prompt string) error {
	m.systemPrompt = prompt
	return nil
}

func (m *MockConnection) SetCloseAfterChat(close bool) {
	m.closeAfterChat = close
}

func (m *MockConnection) GetLastNewsLink() map[string]interface{} {
	return m.lastNewsLink
}

func (m *MockConnection) SetLastNewsLink(link map[string]interface{}) {
	m.lastNewsLink = link
}

// MockLogger implements the Logger interface for testing
type MockLogger struct{}

func (m *MockLogger) Debug(msg string, args ...interface{}) {}
func (m *MockLogger) Info(msg string, args ...interface{})  {}
func (m *MockLogger) Warn(msg string, args ...interface{})  {}
func (m *MockLogger) Error(msg string, args ...interface{}) {}
func (m *MockLogger) WithTag(tag string) types.Logger       { return m }

// MockDialogue implements the Dialogue interface for testing
type MockDialogue struct{}

func (m *MockDialogue) UpdateSystemMessage(message string) error {
	return nil
}

// TestHandleExitIntentFunction tests the handle exit intent function
func TestHandleExitIntentFunction(t *testing.T) {
	function := NewHandleExitIntentFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	// Test basic functionality
	t.Run("BasicExecution", func(t *testing.T) {
		args := map[string]interface{}{
			"say_goodbye": "再见，祝您愉快！",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Equal(t, "退出意图已处理", response.Result)
		assert.Equal(t, "再见，祝您愉快！", response.Response)
		assert.True(t, conn.closeAfterChat)
	})
	
	// Test with default goodbye message
	t.Run("DefaultGoodbyeMessage", func(t *testing.T) {
		args := map[string]interface{}{}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Equal(t, "再见，祝您生活愉快！", response.Response)
	})
	
	// Test tool info
	t.Run("ToolInfo", func(t *testing.T) {
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, HandleExitIntentFunctionName, toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
		assert.NotNil(t, toolInfo.ParamsOneOf)
	})
	
	// Test function metadata
	t.Run("Metadata", func(t *testing.T) {
		assert.Equal(t, HandleExitIntentFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeSystemCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
	})
}

// TestChangeRoleFunction tests the change role function
func TestChangeRoleFunction(t *testing.T) {
	function := NewChangeRoleFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	// Test successful role change
	t.Run("SuccessfulRoleChange", func(t *testing.T) {
		args := map[string]interface{}{
			"role":      "英语老师",
			"role_name": "Lily",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Equal(t, "切换角色已处理", response.Result)
		assert.Contains(t, response.Response.(string), "切换角色成功")
		assert.Contains(t, response.Response.(string), "英语老师")
		assert.Contains(t, response.Response.(string), "Lily")
		
		// Check that system prompt was changed
		assert.Contains(t, conn.systemPrompt, "Lily")
		assert.Contains(t, conn.systemPrompt, "英语老师")
	})
	
	// Test unsupported role
	t.Run("UnsupportedRole", func(t *testing.T) {
		args := map[string]interface{}{
			"role":      "不存在的角色",
			"role_name": "测试",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Equal(t, "不支持的角色", response.Response)
	})
	
	// Test missing parameters
	t.Run("MissingRole", func(t *testing.T) {
		args := map[string]interface{}{
			"role_name": "测试",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.Error(t, err)
		assert.Equal(t, types.ActionError, response.Action)
	})
	
	t.Run("MissingRoleName", func(t *testing.T) {
		args := map[string]interface{}{
			"role": "英语老师",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.Error(t, err)
		assert.Equal(t, types.ActionError, response.Action)
	})
	
	// Test tool info
	t.Run("ToolInfo", func(t *testing.T) {
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, ChangeRoleFunctionName, toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
		assert.NotNil(t, toolInfo.ParamsOneOf)
	})
	
	// Test function metadata
	t.Run("Metadata", func(t *testing.T) {
		assert.Equal(t, ChangeRoleFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeChangeSysPrompt, function.GetType())
		assert.NotNil(t, function.GetDescription())
	})
	
	// Test supported roles
	t.Run("SupportedRoles", func(t *testing.T) {
		supportedRoles := function.GetSupportedRoles()
		assert.Contains(t, supportedRoles, "英语老师")
		assert.Contains(t, supportedRoles, "机车女友")
		assert.Contains(t, supportedRoles, "好奇小男孩")
	})
}

// TestFunctionRegistration tests that functions are properly registered
func TestFunctionRegistration(t *testing.T) {
	// Test that functions are registered in global registry
	// This tests the init() functions in each function file
	
	// Note: Since init() functions run automatically when packages are imported,
	// we can test that the functions are available in the global registry
	
	// This would require access to the global registry, which we'll implement
	// in the integration tests
}

// TestEinoIntegration tests eino schema compatibility
func TestEinoIntegration(t *testing.T) {
	t.Run("HandleExitIntentEinoSchema", func(t *testing.T) {
		function := NewHandleExitIntentFunction()
		toolInfo := function.GetInfo()
		
		require.NotNil(t, toolInfo)
		assert.Equal(t, "handle_exit_intent", toolInfo.Name)
		
		// Test that we can convert to OpenAPI schema
		openAPISchema, err := toolInfo.ParamsOneOf.ToOpenAPIV3()
		require.NoError(t, err)
		assert.NotNil(t, openAPISchema)
	})
	
	t.Run("ChangeRoleEinoSchema", func(t *testing.T) {
		function := NewChangeRoleFunction()
		toolInfo := function.GetInfo()
		
		require.NotNil(t, toolInfo)
		assert.Equal(t, "change_role", toolInfo.Name)
		
		// Test that we can convert to OpenAPI schema
		openAPISchema, err := toolInfo.ParamsOneOf.ToOpenAPIV3()
		require.NoError(t, err)
		assert.NotNil(t, openAPISchema)
	})
}
