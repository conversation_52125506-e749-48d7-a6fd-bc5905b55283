package functions

import (
	"context"
	"testing"
	"time"
	
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
	"xiaozhi-esp32-server-golang/internal/fc_tools/utils"
)

// TestGetTimeFunction tests the time function
func TestGetTimeFunction(t *testing.T) {
	function := NewGetTimeFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	t.Run("BasicTimeQuery", func(t *testing.T) {
		args := map[string]interface{}{
			"timezone": "Asia/Shanghai",
			"format":   "detailed",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.NotNil(t, response.Result)
		
		// Check response contains time information
		responseStr, ok := response.Result.(string)
		require.True(t, ok)
		assert.Contains(t, responseStr, "时间信息")
		assert.Contains(t, responseStr, "公历信息")
		assert.Contains(t, responseStr, "农历信息")
	})
	
	t.Run("SimpleTimeFormat", func(t *testing.T) {
		args := map[string]interface{}{
			"format": "simple",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		
		responseStr := response.Result.(string)
		assert.Contains(t, responseStr, "当前时间")
		assert.Contains(t, responseStr, "农历")
	})
	
	t.Run("LunarTimeFormat", func(t *testing.T) {
		args := map[string]interface{}{
			"format": "lunar",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		
		responseStr := response.Result.(string)
		assert.Contains(t, responseStr, "农历时间信息")
		assert.Contains(t, responseStr, "生肖年")
		assert.Contains(t, responseStr, "干支纪年")
	})
	
	t.Run("TimezoneHandling", func(t *testing.T) {
		args := map[string]interface{}{
			"timezone": "UTC",
			"format":   "simple",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, GetTimeFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeWait, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "get_time", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
	
	t.Run("SupportedTimezones", func(t *testing.T) {
		timezones := function.GetSupportedTimezones()
		assert.Contains(t, timezones, "Asia/Shanghai")
		assert.Contains(t, timezones, "UTC")
		assert.Contains(t, timezones, "America/New_York")
	})
	
	t.Run("SupportedFormats", func(t *testing.T) {
		formats := function.GetSupportedFormats()
		assert.Contains(t, formats, "detailed")
		assert.Contains(t, formats, "simple")
		assert.Contains(t, formats, "lunar")
	})
}

// TestPlayMusicFunction tests the music function
func TestPlayMusicFunction(t *testing.T) {
	function := NewPlayMusicFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	t.Run("PlayMusicWithoutFile", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "play",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "请提供音乐文件路径")
	})
	
	t.Run("PlayMusicWithInvalidFile", func(t *testing.T) {
		args := map[string]interface{}{
			"music_path": "/nonexistent/file.mp3",
			"action":     "play",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "音乐文件无效")
	})
	
	t.Run("PauseWithoutMusic", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "pause",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "当前没有正在播放的音乐")
	})
	
	t.Run("StopMusic", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "stop",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "音乐播放已停止")
	})
	
	t.Run("VolumeControl", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "volume",
			"volume": 75,
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "音量已调节至: 75%")
	})
	
	t.Run("InvalidVolumeRange", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "volume",
			"volume": 150,
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "音量范围应在0-100之间")
	})
	
	t.Run("UnsupportedAction", func(t *testing.T) {
		args := map[string]interface{}{
			"action": "invalid_action",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "不支持的操作")
	})
	
	t.Run("PlayerStatus", func(t *testing.T) {
		status := function.GetPlayerStatus()
		assert.Contains(t, status, "is_playing")
		assert.Contains(t, status, "is_paused")
		assert.Contains(t, status, "current_file")
		assert.Contains(t, status, "volume")
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, PlayMusicFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeSystemCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "play_music", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
}

// TestHassInitFunction tests the Home Assistant initialization function
func TestHassInitFunction(t *testing.T) {
	function := NewHassInitFunction()
	conn := NewMockConnection()
	ctx := context.Background()
	
	t.Run("InitWithoutConfig", func(t *testing.T) {
		args := map[string]interface{}{}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "请提供Home Assistant服务器地址")
	})
	
	t.Run("InitWithPartialConfig", func(t *testing.T) {
		args := map[string]interface{}{
			"base_url": "http://localhost:8123",
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		assert.Contains(t, response.Result.(string), "请提供Home Assistant访问令牌")
	})
	
	t.Run("InitWithFullConfig", func(t *testing.T) {
		// Set up mock configuration
		conn.config = map[string]interface{}{
			"plugins": map[string]interface{}{
				"hass": map[string]interface{}{
					"base_url": "http://localhost:8123",
					"token":    "test_token_123456789",
					"timeout":  30,
				},
			},
		}
		
		args := map[string]interface{}{
			"test_connection": false, // Skip connection test in unit test
			"load_entities":   false, // Skip entity loading in unit test
		}
		
		response, err := function.Execute(ctx, conn, args)
		require.NoError(t, err)
		assert.Equal(t, types.ActionDirectResponse, response.Action)
		
		responseStr := response.Result.(string)
		assert.Contains(t, responseStr, "Home Assistant 初始化完成")
		assert.Contains(t, responseStr, "http://localhost:8123")
	})
	
	t.Run("FunctionMetadata", func(t *testing.T) {
		assert.Equal(t, HassInitFunctionName, function.GetName())
		assert.Equal(t, types.ToolTypeIotCtl, function.GetType())
		assert.NotNil(t, function.GetDescription())
		
		toolInfo := function.GetInfo()
		require.NotNil(t, toolInfo)
		assert.Equal(t, "hass_init", toolInfo.Name)
		assert.NotEmpty(t, toolInfo.Desc)
	})
}

// TestLunarCalendarUtils tests the lunar calendar utilities
func TestLunarCalendarUtils(t *testing.T) {
	t.Run("LunarCalendarCreation", func(t *testing.T) {
		lc := utils.NewLunarCalendar()
		assert.NotNil(t, lc)
	})
	
	t.Run("SolarToLunarConversion", func(t *testing.T) {
		testDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
		lunar := utils.SolarToLunar(testDate)
		
		assert.NotNil(t, lunar)
		assert.Greater(t, lunar.Year, 0)
		assert.GreaterOrEqual(t, lunar.Month, 1)
		assert.LessOrEqual(t, lunar.Month, 12)
		assert.GreaterOrEqual(t, lunar.Day, 1)
		assert.LessOrEqual(t, lunar.Day, 30)
		assert.NotEmpty(t, lunar.YearName)
		assert.NotEmpty(t, lunar.MonthName)
		assert.NotEmpty(t, lunar.DayName)
		assert.NotEmpty(t, lunar.Zodiac)
	})
	
	t.Run("TimeInfoGeneration", func(t *testing.T) {
		testDate := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
		timeInfo := utils.GetTimeInfo(testDate)
		
		assert.Contains(t, timeInfo, "solar_date")
		assert.Contains(t, timeInfo, "solar_weekday")
		assert.Contains(t, timeInfo, "lunar_date")
		assert.Contains(t, timeInfo, "lunar_detail")
		assert.Contains(t, timeInfo, "solar_term")
		assert.Contains(t, timeInfo, "year_name")
		assert.Contains(t, timeInfo, "zodiac")
		assert.Contains(t, timeInfo, "time_string")
	})
	
	t.Run("SolarTermCalculation", func(t *testing.T) {
		testDate := time.Date(2024, 6, 21, 0, 0, 0, 0, time.UTC) // Around summer solstice
		solarTerm := utils.GetCurrentSolarTerm(testDate)
		
		assert.NotEmpty(t, solarTerm)
		// Should be one of the 24 solar terms
		assert.Contains(t, []string{
			"立春", "雨水", "惊蛰", "春分", "清明", "谷雨",
			"立夏", "小满", "芒种", "夏至", "小暑", "大暑",
			"立秋", "处暑", "白露", "秋分", "寒露", "霜降",
			"立冬", "小雪", "大雪", "冬至", "小寒", "大寒",
		}, solarTerm)
	})
	
	t.Run("GlobalLunarCalendar", func(t *testing.T) {
		assert.NotNil(t, utils.GlobalLunarCalendar)
		
		testDate := time.Now()
		lunar := utils.GlobalLunarCalendar.SolarToLunar(testDate)
		assert.NotNil(t, lunar)
	})
}

// TestHassClientUtils tests the Home Assistant client utilities
func TestHassClientUtils(t *testing.T) {
	t.Run("HassClientCreation", func(t *testing.T) {
		config := utils.HassConfig{
			BaseURL: "http://localhost:8123",
			Token:   "test_token",
			Timeout: 30 * time.Second,
		}
		
		client := utils.NewHassClient(config)
		assert.NotNil(t, client)
	})
	
	t.Run("HassClientDefaultTimeout", func(t *testing.T) {
		config := utils.HassConfig{
			BaseURL: "http://localhost:8123",
			Token:   "test_token",
			// No timeout specified
		}
		
		client := utils.NewHassClient(config)
		assert.NotNil(t, client)
	})
	
	t.Run("EntityFiltering", func(t *testing.T) {
		config := utils.HassConfig{
			BaseURL: "http://localhost:8123",
			Token:   "test_token",
		}
		client := utils.NewHassClient(config)
		
		// Mock states
		states := []utils.HassState{
			{EntityID: "light.living_room"},
			{EntityID: "switch.kitchen"},
			{EntityID: "light.bedroom"},
			{EntityID: "sensor.temperature"},
		}
		
		// Filter by domain
		lights := client.FilterEntitiesByDomain(states, "light")
		assert.Len(t, lights, 2)
		assert.Equal(t, "light.living_room", lights[0].EntityID)
		assert.Equal(t, "light.bedroom", lights[1].EntityID)
		
		switches := client.FilterEntitiesByDomain(states, "switch")
		assert.Len(t, switches, 1)
		assert.Equal(t, "switch.kitchen", switches[0].EntityID)
	})
	
	t.Run("EntityNameSearch", func(t *testing.T) {
		config := utils.HassConfig{
			BaseURL: "http://localhost:8123",
			Token:   "test_token",
		}
		client := utils.NewHassClient(config)
		
		// Mock states with friendly names
		states := []utils.HassState{
			{
				EntityID: "light.living_room",
				Attributes: map[string]interface{}{
					"friendly_name": "Living Room Light",
				},
			},
			{
				EntityID: "light.bedroom",
				Attributes: map[string]interface{}{
					"friendly_name": "Bedroom Light",
				},
			},
		}
		
		// Search by name
		found := client.FindEntitiesByName(states, "living")
		assert.Len(t, found, 1)
		assert.Equal(t, "light.living_room", found[0].EntityID)
		
		found = client.FindEntitiesByName(states, "light")
		assert.Len(t, found, 2) // Should find both lights
	})
}
