package fc_tools

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaozhi-esp32-server-golang/internal/fc_tools/config"
	"xiaozhi-esp32-server-golang/internal/fc_tools/monitoring"
	"xiaozhi-esp32-server-golang/internal/fc_tools/types"
)

// TestProductionReadiness tests production readiness features
func TestProductionReadiness(t *testing.T) {
	t.Run("ConfigurationManagement", func(t *testing.T) {
		// Test configuration manager
		cm := config.NewConfigManager("production")
		
		// Test setting and getting configuration
		cm.Set("test.key", "test_value")
		value := cm.GetString("test.key", "default")
		assert.Equal(t, "test_value", value)
		
		// Test default values
		defaultValue := cm.GetString("nonexistent.key", "default")
		assert.Equal(t, "default", defaultValue)
		
		// Test different types
		cm.Set("test.int", 42)
		cm.Set("test.bool", true)
		cm.Set("test.duration", "5m")
		
		assert.Equal(t, 42, cm.GetInt("test.int", 0))
		assert.Equal(t, true, cm.GetBool("test.bool", false))
		assert.Equal(t, 5*time.Minute, cm.GetDuration("test.duration", 0))
	})
	
	t.Run("EnvironmentVariableLoading", func(t *testing.T) {
		// Set test environment variables
		os.Setenv("FC_TOOLS_TEST_STRING", "test_value")
		os.Setenv("FC_TOOLS_TEST_INT", "123")
		os.Setenv("FC_TOOLS_TEST_BOOL", "true")
		os.Setenv("FC_TOOLS_TEST_DURATION", "10m")
		
		defer func() {
			os.Unsetenv("FC_TOOLS_TEST_STRING")
			os.Unsetenv("FC_TOOLS_TEST_INT")
			os.Unsetenv("FC_TOOLS_TEST_BOOL")
			os.Unsetenv("FC_TOOLS_TEST_DURATION")
		}()
		
		cm := config.NewConfigManager("test")
		cm.LoadFromEnv()
		
		// Verify environment variables are loaded
		// Note: This test verifies the loading mechanism works
		// Actual values depend on the LoadFromEnv implementation
		assert.NotNil(t, cm.Get("environment"))
	})
	
	t.Run("MetricsCollection", func(t *testing.T) {
		// Clear existing metrics
		monitoring.GlobalMetricsCollector.ClearMetrics()
		
		// Test counter metrics with same labels (should aggregate)
		labels := map[string]string{
			"function": "test_function",
			"status":   "success",
		}

		monitoring.RecordCounter("test_counter", 1, labels)
		monitoring.RecordCounter("test_counter", 2, labels)
		
		// Test gauge metrics
		monitoring.RecordGauge("test_gauge", 42.5, map[string]string{
			"type": "memory_usage",
		})
		
		// Test timing metrics
		monitoring.RecordTiming("test_timing", 100*time.Millisecond, map[string]string{
			"operation": "database_query",
		})
		
		// Verify metrics are collected
		metrics := monitoring.GlobalMetricsCollector.GetMetrics()
		assert.Greater(t, len(metrics), 0)
		
		// Verify counter aggregation
		counterMetrics := monitoring.GlobalMetricsCollector.GetMetricsByType(monitoring.MetricTypeCounter)
		assert.Len(t, counterMetrics, 1)
		assert.Equal(t, float64(3), counterMetrics[0].Value) // 1 + 2 = 3
		
		// Verify gauge metrics
		gaugeMetrics := monitoring.GlobalMetricsCollector.GetMetricsByType(monitoring.MetricTypeGauge)
		assert.Len(t, gaugeMetrics, 1)
		assert.Equal(t, 42.5, gaugeMetrics[0].Value)
		
		// Verify timing metrics
		timingMetrics := monitoring.GlobalMetricsCollector.GetMetricsByType(monitoring.MetricTypeTiming)
		assert.Len(t, timingMetrics, 1)
		assert.Equal(t, float64(100), timingMetrics[0].Value) // 100ms
	})
	
	t.Run("FunctionTracking", func(t *testing.T) {
		// Clear existing metrics
		monitoring.GlobalFunctionTracker.ClearMetrics()
		
		// Track successful executions
		monitoring.TrackFunctionExecution("test_function", 50*time.Millisecond, true, "")
		monitoring.TrackFunctionExecution("test_function", 75*time.Millisecond, true, "")
		monitoring.TrackFunctionExecution("test_function", 25*time.Millisecond, false, "test error")
		
		// Get function metrics
		metrics := monitoring.GlobalFunctionTracker.GetFunctionMetrics("test_function")
		require.NotNil(t, metrics)
		
		assert.Equal(t, int64(3), metrics.ExecutionCount)
		assert.Equal(t, int64(2), metrics.SuccessCount)
		assert.Equal(t, int64(1), metrics.ErrorCount)
		assert.Equal(t, "test error", metrics.LastError)
		
		// Check success rate
		successRate := monitoring.GlobalFunctionTracker.GetSuccessRate("test_function")
		assert.InDelta(t, 0.666, successRate, 0.01) // 2/3 ≈ 0.666
		
		// Check average duration
		expectedAvg := (50 + 75 + 25) / 3 // 50ms average
		assert.Equal(t, time.Duration(expectedAvg)*time.Millisecond, metrics.AverageDuration)
	})
	
	t.Run("SystemMetrics", func(t *testing.T) {
		// Get system metrics
		systemMetrics := monitoring.GetSystemMetrics()
		
		assert.NotNil(t, systemMetrics)
		assert.NotNil(t, systemMetrics.FunctionMetrics)
		assert.NotNil(t, systemMetrics.PerformanceMetrics)
		assert.False(t, systemMetrics.Timestamp.IsZero())
	})
	
	t.Run("MetricsDisabling", func(t *testing.T) {
		// Test disabling metrics collection
		monitoring.GlobalMetricsCollector.SetEnabled(false)
		assert.False(t, monitoring.GlobalMetricsCollector.IsEnabled())
		
		initialCount := monitoring.GlobalMetricsCollector.GetMetricsCount()
		
		// Try to record metrics when disabled
		monitoring.RecordCounter("disabled_test", 1, nil)
		
		// Should not increase count
		assert.Equal(t, initialCount, monitoring.GlobalMetricsCollector.GetMetricsCount())
		
		// Re-enable for other tests
		monitoring.GlobalMetricsCollector.SetEnabled(true)
	})
}

// TestProductionIntegration tests production integration scenarios
func TestProductionIntegration(t *testing.T) {
	conn := NewMockConnection()
	fcTools := NewFCTools(conn)
	
	// Set up production-like configuration
	conn.config = map[string]interface{}{
		"environment": "production",
		"debug":       false,
		"monitoring": map[string]interface{}{
			"enabled":         true,
			"metrics_enabled": true,
		},
		"cache": map[string]interface{}{
			"enabled":     true,
			"default_ttl": "5m",
		},
		"plugins": map[string]interface{}{
			"get_weather": map[string]interface{}{
				"api_host":         "devapi.qweather.com",
				"api_key":          "production_api_key",
				"default_location": "北京",
			},
		},
	}
	
	t.Run("ProductionFunctionExecution", func(t *testing.T) {
		// Register a test function with monitoring
		testFunction := &MockFunctionTool{
			name:        "production_test_function",
			description: "Production test function",
			toolType:    types.ToolTypeSystemCtl,
			executeFunc: func(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
				// Simulate some work
				time.Sleep(10 * time.Millisecond)
				
				// Record custom metrics
				monitoring.RecordCounter("function_calls", 1, map[string]string{
					"function": "production_test_function",
					"status":   "success",
				})
				
				return types.NewActionResponse(types.ActionDirectResponse, "Production test successful", nil), nil
			},
		}
		
		err := fcTools.RegisterFunction("production_test_function", testFunction)
		require.NoError(t, err)
		
		// Execute function multiple times to generate metrics
		ctx := context.Background()
		for i := 0; i < 5; i++ {
			start := time.Now()
			
			response, err := fcTools.ExecuteFunction(ctx, conn, "production_test_function", map[string]interface{}{})
			duration := time.Since(start)
			
			require.NoError(t, err)
			assert.Equal(t, types.ActionDirectResponse, response.Action)
			
			// Track execution metrics
			monitoring.TrackFunctionExecution("production_test_function", duration, true, "")
		}
		
		// Verify metrics were collected
		functionMetrics := monitoring.GlobalFunctionTracker.GetFunctionMetrics("production_test_function")
		require.NotNil(t, functionMetrics)
		assert.Equal(t, int64(5), functionMetrics.ExecutionCount)
		assert.Equal(t, int64(5), functionMetrics.SuccessCount)
		assert.Equal(t, int64(0), functionMetrics.ErrorCount)
		
		// Verify success rate
		successRate := monitoring.GlobalFunctionTracker.GetSuccessRate("production_test_function")
		assert.Equal(t, 1.0, successRate)
	})
	
	t.Run("ErrorHandlingWithMetrics", func(t *testing.T) {
		// Register a function that sometimes fails
		errorFunction := &MockFunctionTool{
			name:        "error_test_function",
			description: "Function that sometimes fails",
			toolType:    types.ToolTypeSystemCtl,
			executeFunc: func(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
				// Fail on odd executions
				if time.Now().UnixNano()%2 == 1 {
					monitoring.RecordCounter("function_errors", 1, map[string]string{
						"function": "error_test_function",
						"error":    "simulated_error",
					})
					return types.NewActionResponse(types.ActionError, "Simulated error", nil), 
						   fmt.Errorf("simulated error")
				}
				
				monitoring.RecordCounter("function_calls", 1, map[string]string{
					"function": "error_test_function",
					"status":   "success",
				})
				return types.NewActionResponse(types.ActionDirectResponse, "Success", nil), nil
			},
		}
		
		err := fcTools.RegisterFunction("error_test_function", errorFunction)
		require.NoError(t, err)
		
		// Execute function multiple times
		ctx := context.Background()
		successCount := 0
		errorCount := 0
		
		for i := 0; i < 10; i++ {
			start := time.Now()
			
			response, err := fcTools.ExecuteFunction(ctx, conn, "error_test_function", map[string]interface{}{})
			duration := time.Since(start)
			
			if err != nil {
				errorCount++
				monitoring.TrackFunctionExecution("error_test_function", duration, false, err.Error())
			} else {
				successCount++
				monitoring.TrackFunctionExecution("error_test_function", duration, true, "")
			}
			
			// Response should always be valid, even on error
			assert.NotNil(t, response)
		}
		
		// Verify we had both successes and errors
		assert.Greater(t, successCount, 0)
		assert.Greater(t, errorCount, 0)
		
		// Verify metrics were tracked
		functionMetrics := monitoring.GlobalFunctionTracker.GetFunctionMetrics("error_test_function")
		require.NotNil(t, functionMetrics)
		assert.Equal(t, int64(10), functionMetrics.ExecutionCount)
		assert.Equal(t, int64(successCount), functionMetrics.SuccessCount)
		assert.Equal(t, int64(errorCount), functionMetrics.ErrorCount)
	})
	
	t.Run("ConcurrentExecution", func(t *testing.T) {
		// Test concurrent function execution
		concurrentFunction := &MockFunctionTool{
			name:        "concurrent_test_function",
			description: "Function for concurrent testing",
			toolType:    types.ToolTypeSystemCtl,
			executeFunc: func(ctx context.Context, conn types.Connection, args map[string]interface{}) (*types.ActionResponse, error) {
				// Simulate some work
				time.Sleep(5 * time.Millisecond)
				return types.NewActionResponse(types.ActionDirectResponse, "Concurrent success", nil), nil
			},
		}
		
		err := fcTools.RegisterFunction("concurrent_test_function", concurrentFunction)
		require.NoError(t, err)
		
		// Execute function concurrently
		const numGoroutines = 10
		const executionsPerGoroutine = 5
		
		done := make(chan bool, numGoroutines)
		
		for i := 0; i < numGoroutines; i++ {
			go func() {
				defer func() { done <- true }()
				
				for j := 0; j < executionsPerGoroutine; j++ {
					ctx := context.Background()
					start := time.Now()
					
					response, err := fcTools.ExecuteFunction(ctx, conn, "concurrent_test_function", map[string]interface{}{})
					duration := time.Since(start)
					
					assert.NoError(t, err)
					assert.Equal(t, types.ActionDirectResponse, response.Action)
					
					monitoring.TrackFunctionExecution("concurrent_test_function", duration, true, "")
				}
			}()
		}
		
		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}
		
		// Verify all executions were tracked
		functionMetrics := monitoring.GlobalFunctionTracker.GetFunctionMetrics("concurrent_test_function")
		require.NotNil(t, functionMetrics)
		assert.Equal(t, int64(numGoroutines*executionsPerGoroutine), functionMetrics.ExecutionCount)
		assert.Equal(t, int64(numGoroutines*executionsPerGoroutine), functionMetrics.SuccessCount)
		assert.Equal(t, int64(0), functionMetrics.ErrorCount)
	})
}
