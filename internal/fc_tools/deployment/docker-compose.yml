version: '3.8'

services:
  fc-tools:
    image: fc-tools:latest
    container_name: fc-tools-server
    restart: unless-stopped
    
    environment:
      # 基本配置
      - FC_TOOLS_ENV=production
      - FC_TOOLS_DEBUG=false
      
      # 日志配置
      - FC_TOOLS_LOG_LEVEL=info
      - FC_TOOLS_LOG_FORMAT=json
      - FC_TOOLS_LOG_OUTPUT=stdout
      
      # 缓存配置
      - FC_TOOLS_CACHE_ENABLED=true
      - FC_TOOLS_CACHE_DEFAULT_TTL=5m
      - FC_TOOLS_CACHE_CLEANUP_INTERVAL=5m
      - FC_TOOLS_CACHE_MAX_SIZE=1000
      
      # HTTP 配置
      - FC_TOOLS_HTTP_TIMEOUT=30s
      - FC_TOOLS_HTTP_RETRIES=3
      - FC_TOOLS_HTTP_RETRY_DELAY=1s
      - FC_TOOLS_HTTP_MAX_CONNECTIONS=100
      
      # 监控配置
      - FC_TOOLS_MONITORING_ENABLED=true
      - FC_TOOLS_MONITORING_METRICS_ENABLED=true
      - FC_TOOLS_MONITORING_REPORT_INTERVAL=1m
      - FC_TOOLS_MONITORING_RETENTION_DAYS=7
      
      # 天气 API 配置
      - FC_TOOLS_WEATHER_API_HOST=devapi.qweather.com
      - FC_TOOLS_WEATHER_API_KEY=${WEATHER_API_KEY}
      - FC_TOOLS_WEATHER_DEFAULT_LOCATION=北京
      
      # Home Assistant 配置
      - FC_TOOLS_HASS_BASE_URL=${HASS_BASE_URL}
      - FC_TOOLS_HASS_TOKEN=${HASS_TOKEN}
      - FC_TOOLS_HASS_TIMEOUT=30
    
    volumes:
      # 配置文件挂载
      - ./config:/app/config:ro
      # 日志文件挂载
      - ./logs:/app/logs
      # 音乐文件挂载 (如果需要本地音乐播放)
      - ./music:/app/music:ro
      # 缓存数据持久化
      - fc-tools-cache:/app/cache
    
    ports:
      - "8080:8080"  # HTTP API 端口
      - "9090:9090"  # 监控指标端口
    
    networks:
      - fc-tools-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M
    
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Redis 缓存 (可选，用于分布式缓存)
  redis:
    image: redis:7-alpine
    container_name: fc-tools-redis
    restart: unless-stopped
    
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    
    volumes:
      - redis-data:/data
    
    ports:
      - "6379:6379"
    
    networks:
      - fc-tools-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Prometheus 监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: fc-tools-prometheus
    restart: unless-stopped
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    ports:
      - "9091:9090"
    
    networks:
      - fc-tools-network
    
    depends_on:
      - fc-tools

  # Grafana 仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: fc-tools-grafana
    restart: unless-stopped
    
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    
    ports:
      - "3000:3000"
    
    networks:
      - fc-tools-network
    
    depends_on:
      - prometheus

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: fc-tools-nginx
    restart: unless-stopped
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    
    ports:
      - "80:80"
      - "443:443"
    
    networks:
      - fc-tools-network
    
    depends_on:
      - fc-tools

volumes:
  fc-tools-cache:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  fc-tools-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
