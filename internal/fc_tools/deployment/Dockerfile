# Multi-stage build for FC Tools
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    gcc \
    musl-dev

# 设置时区
ENV TZ=Asia/Shanghai

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o fc-tools \
    ./cmd/fc-tools

# 运行测试 (可选，在构建时验证)
RUN go test ./internal/fc_tools/...

# 生产镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    ffmpeg \
    mpg123 \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非 root 用户
RUN addgroup -g 1001 -S fctools && \
    adduser -u 1001 -S fctools -G fctools

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/config /app/logs /app/cache /app/music && \
    chown -R fctools:fctools /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/fc-tools /app/fc-tools

# 复制配置文件模板
COPY --from=builder /app/internal/fc_tools/deployment/config/ /app/config/

# 设置权限
RUN chmod +x /app/fc-tools && \
    chown fctools:fctools /app/fc-tools

# 切换到非 root 用户
USER fctools

# 暴露端口
EXPOSE 8080 9090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 设置环境变量
ENV FC_TOOLS_ENV=production
ENV FC_TOOLS_LOG_LEVEL=info
ENV FC_TOOLS_LOG_FORMAT=json

# 启动命令
ENTRYPOINT ["/app/fc-tools"]
CMD ["--config", "/app/config/config.json"]

# 元数据标签
LABEL maintainer="FC Tools Team" \
      version="1.0.0" \
      description="FC Tools - Go Implementation" \
      org.opencontainers.image.title="FC Tools" \
      org.opencontainers.image.description="Function calling tools for intelligent assistants" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.created="2024-01-01T00:00:00Z" \
      org.opencontainers.image.source="https://github.com/your-org/fc-tools" \
      org.opencontainers.image.licenses="MIT"
