package manager_api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
	"xiaozhi-esp32-server-golang/logger"
)

// URLHealth 记录单个URL的健康状态
type URLHealth struct {
	URL              string
	Healthy          bool
	LastCheck        time.Time
	ConsecutiveFails int64 // 使用atomic操作
	ResponseTime     time.Duration
}

// HTTPClient implements the ManagerAPIClient interface with multi-URL support
type HTTPClient struct {
	config      *Config
	httpClient  *http.Client
	urlHealths  []*URLHealth // 多个URL的健康状态
	currentURL  int64        // 当前使用的URL索引，使用atomic操作
	metrics     *types.ServiceMetrics
	configCache *types.ConfigCache
	mutex       sync.RWMutex
}

// NewHTTPClient creates a new HTTP client for manager-api with multi-URL support
func NewHTTPClient(config *Config) (*HTTPClient, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	httpClient := &http.Client{
		Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 初始化URL健康状态
	urlHealths := make([]*URLHealth, len(config.ParsedURLs))
	for i, urlStr := range config.ParsedURLs {
		// 验证URL格式
		if _, err := url.Parse(urlStr); err != nil {
			return nil, ErrInvalidConfig{Field: "base_url", Message: fmt.Sprintf("invalid URL format: %s", urlStr)}
		}

		urlHealths[i] = &URLHealth{
			URL:          urlStr,
			Healthy:      true, // 初始假设健康
			LastCheck:    time.Now(),
			ResponseTime: 0,
		}
	}

	client := &HTTPClient{
		config:     config,
		httpClient: httpClient,
		urlHealths: urlHealths,
		metrics:    types.NewServiceMetrics(),
		configCache: &types.ConfigCache{
			AgentConfigs:  make(map[string]*types.AgentModelsConfig),
			RequestCounts: make(map[string]int),
			ErrorCounts:   make(map[string]int),
			TTL:           time.Duration(config.CacheTTLSeconds) * time.Second,
		},
	}

	return client, nil
}

// getHealthyURL 获取健康的URL，优先使用响应时间最短的
func (c *HTTPClient) getHealthyURL() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	var bestURL *URLHealth
	for _, urlHealth := range c.urlHealths {
		if urlHealth.Healthy {
			if bestURL == nil || urlHealth.ResponseTime < bestURL.ResponseTime {
				bestURL = urlHealth
			}
		}
	}

	if bestURL != nil {
		return bestURL.URL
	}

	// 如果没有健康的URL，返回第一个
	if len(c.urlHealths) > 0 {
		return c.urlHealths[0].URL
	}

	return "http://localhost:8080" // fallback
}

// updateURLHealth 更新URL健康状态
func (c *HTTPClient) updateURLHealth(url string, healthy bool, responseTime time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	for _, urlHealth := range c.urlHealths {
		if urlHealth.URL == url {
			urlHealth.Healthy = healthy
			urlHealth.LastCheck = time.Now()
			urlHealth.ResponseTime = responseTime
			if healthy {
				atomic.StoreInt64(&urlHealth.ConsecutiveFails, 0)
			} else {
				atomic.AddInt64(&urlHealth.ConsecutiveFails, 1)
			}
			break
		}
	}
}

// getAllURLsHealth 获取所有URL的健康状态
func (c *HTTPClient) getAllURLsHealth() []*URLHealth {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make([]*URLHealth, len(c.urlHealths))
	copy(result, c.urlHealths)
	return result
}

// GetServerConfig retrieves server configuration from manager-api
func (c *HTTPClient) GetServerConfig(ctx context.Context) (*types.ServerConfig, error) {
	const endpoint = "/config/server-base"

	// Check cache first
	c.mutex.RLock()
	if c.configCache.ServerConfig != nil && !c.configCache.IsExpired() {
		c.mutex.RUnlock()
		c.metrics.RecordCacheHit()
		return c.configCache.ServerConfig, nil
	}
	c.mutex.RUnlock()
	c.metrics.RecordCacheMiss()

	startTime := time.Now()
	var config *types.ServerConfig
	var err error

	// Perform request with retry logic
	err = c.retryRequest(ctx, endpoint, func() error {
		config, err = c.getServerConfigRequest(ctx)
		return err
	})

	duration := time.Since(startTime)
	success := err == nil
	c.metrics.RecordRequest(endpoint, duration, success)

	if err != nil {
		c.metrics.RecordError(endpoint, getErrorType(err), err.Error(), getStatusCode(err))
		return nil, err
	}

	// Cache the result
	c.mutex.Lock()
	c.configCache.ServerConfig = config
	c.configCache.LastUpdated = time.Now()
	c.mutex.Unlock()

	return config, nil
}

// getServerConfigRequest performs the actual HTTP request for server config
func (c *HTTPClient) getServerConfigRequest(ctx context.Context) (*types.ServerConfig, error) {
	req, err := c.createRequest(ctx, "POST", "/config/server-base", nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, ErrNetwork{Operation: "get server config", Err: err}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleHTTPError(resp)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, ErrNetwork{Operation: "read response body", Err: err}
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, ErrAPIRequest{StatusCode: resp.StatusCode, Message: "invalid JSON response"}
	}

	// Convert to ServerConfig
	config := &types.ServerConfig{
		Additional: response,
	}

	// Extract known fields
	if ws, ok := response["websocket"].(string); ok {
		config.WebSocket = ws
	}
	if mcp, ok := response["mcp_endpoint"].(string); ok {
		config.MCPEndpoint = mcp
	}
	if vp, ok := response["voice_print"].(string); ok {
		config.VoicePrint = vp
	}
	if tz, ok := response["timezone_offset"].(string); ok {
		config.TimezoneOffset = tz
	}
	if ve, ok := response["vision_explain"].(string); ok {
		config.VisionExplain = ve
	}
	if ip, ok := response["ip"].(string); ok {
		config.IP = ip
	}
	if port, ok := response["port"].(float64); ok {
		config.Port = int(port)
	}
	if httpPort, ok := response["http_port"].(float64); ok {
		config.HTTPPort = int(httpPort)
	}

	// Extract auth configuration
	if authData, ok := response["auth"].(map[string]interface{}); ok {
		config.Auth = types.AuthConfig{}
		if enabled, ok := authData["enabled"].(bool); ok {
			config.Auth.Enabled = enabled
		}
		if tokens, ok := authData["tokens"].([]interface{}); ok {
			for _, token := range tokens {
				if tokenData, ok := token.(map[string]interface{}); ok {
					tokenConfig := types.TokenConfig{}
					if t, ok := tokenData["token"].(string); ok {
						tokenConfig.Token = t
					}
					if n, ok := tokenData["name"].(string); ok {
						tokenConfig.Name = n
					}
					config.Auth.Tokens = append(config.Auth.Tokens, tokenConfig)
				}
			}
		}
	}

	return config, nil
}

// GetAgentModels retrieves agent model configuration for a specific device
func (c *HTTPClient) GetAgentModels(ctx context.Context, req *types.AgentModelsRequest) (*types.AgentModelsConfig, error) {
	const endpoint = "/config/agent-models"

	// Generate cache key
	cacheKey := types.GetDeviceKey(req.MacAddress, req.ClientID, req.SelectedModule)

	// Check cache first
	c.mutex.RLock()
	if cachedConfig, exists := c.configCache.AgentConfigs[cacheKey]; exists && !c.configCache.IsExpired() {
		c.mutex.RUnlock()
		c.metrics.RecordCacheHit()
		return cachedConfig, nil
	}
	c.mutex.RUnlock()
	c.metrics.RecordCacheMiss()

	startTime := time.Now()
	var config *types.AgentModelsConfig
	var err error

	// Perform request with retry logic
	err = c.retryRequest(ctx, endpoint, func() error {
		config, err = c.getAgentModelsRequest(ctx, req)
		return err
	})

	duration := time.Since(startTime)
	success := err == nil
	c.metrics.RecordRequest(endpoint, duration, success)

	if err != nil {
		c.metrics.RecordError(endpoint, getErrorType(err), err.Error(), getStatusCode(err))
		return nil, err
	}

	// Cache the result
	c.mutex.Lock()
	c.configCache.AgentConfigs[cacheKey] = config
	c.configCache.LastUpdated = time.Now()
	c.mutex.Unlock()

	return config, nil
}

// getAgentModelsRequest performs the actual HTTP request for agent models config
func (c *HTTPClient) getAgentModelsRequest(ctx context.Context, req *types.AgentModelsRequest) (*types.AgentModelsConfig, error) {
	body, err := json.Marshal(req)
	if err != nil {
		return nil, ErrAPIRequest{Message: "failed to marshal request"}
	}

	httpReq, err := c.createRequest(ctx, "POST", "/config/agent-models", bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, ErrNetwork{Operation: "get agent models", Err: err}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleHTTPError(resp)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, ErrNetwork{Operation: "read response body", Err: err}
	}

	// 尝试解析包装的响应格式
	type responseVar struct {
		Code int                      `json:"code"`
		Msg  string                   `json:"msg"`
		Data *types.AgentModelsConfig `json:"data"`
	}

	var response responseVar
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, ErrAPIRequest{StatusCode: resp.StatusCode, Message: "invalid JSON response"}
	}

	if response.Code != 0 {
		return nil, ErrAPIRequest{StatusCode: resp.StatusCode, Message: response.Msg, Code: fmt.Sprintf("%d", response.Code)}
	}

	return response.Data, nil
}

// SaveMemory saves device summary memory to manager-api
func (c *HTTPClient) SaveMemory(ctx context.Context, macAddress string, req *types.MemoryRequest) error {
	endpoint := fmt.Sprintf("/agent/saveMemory/%s", macAddress)

	startTime := time.Now()
	var err error

	// Perform request with retry logic
	err = c.retryRequest(ctx, endpoint, func() error {
		return c.saveMemoryRequest(ctx, macAddress, req)
	})

	duration := time.Since(startTime)
	success := err == nil
	c.metrics.RecordRequest(endpoint, duration, success)
	c.metrics.MemoryOperations++

	if err != nil {
		c.metrics.RecordError(endpoint, getErrorType(err), err.Error(), getStatusCode(err))
		return err
	}

	return nil
}

// saveMemoryRequest performs the actual HTTP request for saving memory
func (c *HTTPClient) saveMemoryRequest(ctx context.Context, macAddress string, req *types.MemoryRequest) error {
	body, err := json.Marshal(req)
	if err != nil {
		return ErrAPIRequest{Message: "failed to marshal memory request"}
	}

	endpoint := fmt.Sprintf("/agent/saveMemory/%s", macAddress)
	httpReq, err := c.createRequest(ctx, "PUT", endpoint, bytes.NewReader(body))
	if err != nil {
		return err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return ErrNetwork{Operation: "save memory", Err: err}
	}
	defer resp.Body.Close()
	respBody, _ := io.ReadAll(resp.Body)
	logger.Infof("saveMemory response body: %s", string(respBody))
	if resp.StatusCode != http.StatusOK {
		return c.handleHTTPError(resp)
	}

	return nil
}

// ReportChatHistory reports chat history and audio data to manager-api
func (c *HTTPClient) ReportChatHistory(ctx context.Context, req *types.ChatHistoryRequest) error {
	const endpoint = "/agent/chat-history/report"

	startTime := time.Now()
	var err error

	// Perform request with retry logic
	err = c.retryRequest(ctx, endpoint, func() error {
		return c.reportChatHistoryRequest(ctx, req)
	})

	duration := time.Since(startTime)
	success := err == nil
	c.metrics.RecordRequest(endpoint, duration, success)
	c.metrics.ChatReports++

	if err != nil {
		c.metrics.RecordError(endpoint, getErrorType(err), err.Error(), getStatusCode(err))
		return err
	}

	return nil
}

// reportChatHistoryRequest performs the actual HTTP request for reporting chat history
func (c *HTTPClient) reportChatHistoryRequest(ctx context.Context, req *types.ChatHistoryRequest) error {
	body, err := json.Marshal(req)
	if err != nil {
		return ErrAPIRequest{Message: "failed to marshal chat history request"}
	}

	httpReq, err := c.createRequest(ctx, "POST", "/agent/chat-history/report", bytes.NewReader(body))
	if err != nil {
		return err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return ErrNetwork{Operation: "report chat history", Err: err}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleHTTPError(resp)
	}

	return nil
}

// HealthCheck performs a health check against all manager-api URLs
func (c *HTTPClient) HealthCheck(ctx context.Context) error {
	const endpoint = "/health"

	// 同时检查所有URL的健康状态
	urlHealths := c.getAllURLsHealth()
	var wg sync.WaitGroup
	healthyCount := int64(0)

	for _, urlHealth := range urlHealths {
		wg.Add(1)
		go func(uh *URLHealth) {
			defer wg.Done()

			startTime := time.Now()
			healthy := c.checkSingleURL(ctx, uh.URL, endpoint)
			responseTime := time.Since(startTime)

			c.updateURLHealth(uh.URL, healthy, responseTime)
			if healthy {
				atomic.AddInt64(&healthyCount, 1)
			}
		}(urlHealth)
	}

	wg.Wait()

	// 更新整体健康状态
	overallHealthy := healthyCount > 0
	c.metrics.UpdateHealthStatus(overallHealthy)

	if !overallHealthy {
		return ErrServiceUnavailable{Service: "manager-api", Reason: "all URLs are unhealthy"}
	}

	return nil
}

// checkSingleURL 检查单个URL的健康状态
func (c *HTTPClient) checkSingleURL(ctx context.Context, baseURL, endpoint string) bool {
	fullURL := strings.TrimSuffix(baseURL, "/") + endpoint

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		logger.Errorf("Failed to create request for health check: %v", err)
		return false
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Secret)
	req.Header.Set("User-Agent", "xiaozhi-backend-server/1.0")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// createRequest creates an HTTP request with authentication using healthy URL
func (c *HTTPClient) createRequest(ctx context.Context, method, path string, body io.Reader) (*http.Request, error) {
	// 获取健康的URL
	urlStr := c.getHealthyURL()
	newUrl := strings.TrimSuffix(urlStr, "/") + path
	sk := c.config.Secret
	req, err := http.NewRequestWithContext(ctx, method, newUrl, body)
	if err != nil {
		return nil, ErrAPIRequest{Message: "failed to create request"}
	}

	// Add authentication header
	req.Header.Set("Authorization", "Bearer "+sk)
	req.Header.Set("User-Agent", "xiaozhi-backend-server/1.0")

	return req, nil
}

// retryRequest performs a request with retry logic and URL failover
func (c *HTTPClient) retryRequest(ctx context.Context, endpoint string, operation func() error) error {
	var lastErr error
	urlHealths := c.getAllURLsHealth()

	// 尝试所有健康的URL
	for _, urlHealth := range urlHealths {
		if !urlHealth.Healthy {
			continue
		}

		// 每个URL都进行重试
		for attempt := 0; attempt <= c.config.RetryAttempts; attempt++ {
			if attempt > 0 {
				c.metrics.RecordRetry(endpoint)

				// Calculate exponential backoff delay
				delay := time.Duration(c.config.RetryDelayMs) * time.Millisecond
				backoffDelay := time.Duration(math.Pow(2, float64(attempt-1))) * delay

				maxDelay := time.Duration(c.config.MaxRetryDelayMs) * time.Millisecond
				if backoffDelay > maxDelay {
					backoffDelay = maxDelay
				}

				logger.Warnf("Retrying request to %s after %v (attempt %d/%d)",
					endpoint, backoffDelay, attempt, c.config.RetryAttempts)

				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(backoffDelay):
				}
			}

			err := operation()
			if err == nil {
				return nil
			}

			lastErr = err

			// 检查是否为可重试的错误
			if !IsRetryable(err) {
				logger.Errorf("Non-retryable error for %s on %s: %v", endpoint, urlHealth.URL, err)
				break
			}

			// Check for authentication errors (don't retry)
			if IsAuthError(err) {
				logger.Errorf("Authentication error for %s on %s: %v", endpoint, urlHealth.URL, err)
				return err // 认证错误不尝试其他URL
			}

			logger.Warnf("Retryable error for %s on %s: %v", endpoint, urlHealth.URL, err)
		}
	}

	return lastErr
}

// handleHTTPError handles HTTP error responses
func (c *HTTPClient) handleHTTPError(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ErrAPIRequest{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("HTTP %d: failed to read error response", resp.StatusCode),
		}
	}

	// Try to parse error response
	var errorResp struct {
		Message string `json:"message"`
		Code    string `json:"code"`
	}

	if json.Unmarshal(body, &errorResp) == nil && errorResp.Message != "" {
		// Handle specific business logic errors
		if errorResp.Code == "10041" {
			return ErrAPIRequest{
				StatusCode: resp.StatusCode,
				Message:    "Device not found: " + errorResp.Message,
				Code:       errorResp.Code,
			}
		}
		if errorResp.Code == "10042" {
			return ErrAPIRequest{
				StatusCode: resp.StatusCode,
				Message:    "Device bind error: " + errorResp.Message,
				Code:       errorResp.Code,
			}
		}

		return ErrAPIRequest{
			StatusCode: resp.StatusCode,
			Message:    errorResp.Message,
			Code:       errorResp.Code,
		}
	}

	// Handle authentication errors
	if resp.StatusCode == http.StatusUnauthorized {
		return ErrAuthentication{Message: "Invalid or missing authentication token"}
	}

	return ErrAPIRequest{
		StatusCode: resp.StatusCode,
		Message:    fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
	}
}

// GetMetrics returns current client metrics
func (c *HTTPClient) GetMetrics() *types.ServiceMetrics {
	c.metrics.UpdateUptime()
	return c.metrics.GetSnapshot()
}

// IsHealthy returns current health status
func (c *HTTPClient) IsHealthy() bool {
	return c.metrics.IsHealthy
}

// ClearCache clears all cached configurations
func (c *HTTPClient) ClearCache() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.configCache.ServerConfig = nil
	c.configCache.AgentConfigs = make(map[string]*types.AgentModelsConfig)
	c.configCache.LastUpdated = time.Time{}
}

// Helper functions

func getErrorType(err error) string {
	switch err.(type) {
	case ErrAPIRequest:
		return "api_request"
	case ErrNetwork:
		return "network"
	case ErrTimeout:
		return "timeout"
	case ErrAuthentication:
		return "authentication"
	case ErrServiceUnavailable:
		return "service_unavailable"
	default:
		return "unknown"
	}
}

func getStatusCode(err error) int {
	if apiErr, ok := err.(ErrAPIRequest); ok {
		return apiErr.StatusCode
	}
	return 0
}
