package types

import (
	"sync"
	"time"
)

// ServiceMetrics represents metrics for the manager-api service
type ServiceMetrics struct {
	// Service status
	IsHealthy       bool      `json:"is_healthy"`
	LastHealthCheck time.Time `json:"last_health_check"`
	StartTime       time.Time `json:"start_time"`
	Uptime          string    `json:"uptime"`

	// Request metrics
	TotalRequests      int64            `json:"total_requests"`
	SuccessfulRequests int64            `json:"successful_requests"`
	FailedRequests     int64            `json:"failed_requests"`
	RequestsByEndpoint map[string]int64 `json:"requests_by_endpoint"`

	// Response time metrics
	AverageResponseTime time.Duration   `json:"average_response_time"`
	MinResponseTime     time.Duration   `json:"min_response_time"`
	MaxResponseTime     time.Duration   `json:"max_response_time"`
	ResponseTimes       []time.Duration `json:"-"` // Not exported in JSON

	// Error metrics
	ErrorsByType     map[string]int64 `json:"errors_by_type"`
	ErrorsByEndpoint map[string]int64 `json:"errors_by_endpoint"`
	RecentErrors     []ErrorRecord    `json:"recent_errors"`

	// Cache metrics
	CacheHits    int64   `json:"cache_hits"`
	CacheMisses  int64   `json:"cache_misses"`
	CacheHitRate float64 `json:"cache_hit_rate"`
	CacheSize    int     `json:"cache_size"`

	// Memory and chat metrics
	MemoryOperations   int64 `json:"memory_operations"`
	ChatReports        int64 `json:"chat_reports"`
	ConfigRefreshCount int64 `json:"config_refresh_count"`

	// Rate limiting
	RateLimitedRequests int64 `json:"rate_limited_requests"`

	// Retry metrics
	TotalRetries      int64            `json:"total_retries"`
	RetriesByEndpoint map[string]int64 `json:"retries_by_endpoint"`

	mutex sync.RWMutex `json:"-"`
}

// ErrorRecord represents an error record for monitoring
type ErrorRecord struct {
	Timestamp  time.Time `json:"timestamp"`
	Endpoint   string    `json:"endpoint"`
	ErrorType  string    `json:"error_type"`
	Message    string    `json:"message"`
	StatusCode int       `json:"status_code,omitempty"`
}

// NewServiceMetrics creates a new ServiceMetrics instance
func NewServiceMetrics() *ServiceMetrics {
	return &ServiceMetrics{
		StartTime:          time.Now(),
		RequestsByEndpoint: make(map[string]int64),
		ErrorsByType:       make(map[string]int64),
		ErrorsByEndpoint:   make(map[string]int64),
		RetriesByEndpoint:  make(map[string]int64),
		ResponseTimes:      make([]time.Duration, 0, 1000), // Keep last 1000 response times
		RecentErrors:       make([]ErrorRecord, 0, 100),    // Keep last 100 errors
		MinResponseTime:    time.Hour,                      // Initialize to a high value
	}
}

// RecordRequest records a request
func (sm *ServiceMetrics) RecordRequest(endpoint string, duration time.Duration, success bool) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.TotalRequests++
	sm.RequestsByEndpoint[endpoint]++

	if success {
		sm.SuccessfulRequests++
	} else {
		sm.FailedRequests++
	}

	// Update response time metrics
	sm.ResponseTimes = append(sm.ResponseTimes, duration)
	if len(sm.ResponseTimes) > 1000 {
		sm.ResponseTimes = sm.ResponseTimes[1:] // Keep only last 1000
	}

	if duration < sm.MinResponseTime {
		sm.MinResponseTime = duration
	}
	if duration > sm.MaxResponseTime {
		sm.MaxResponseTime = duration
	}

	// Calculate average response time
	var total time.Duration
	for _, rt := range sm.ResponseTimes {
		total += rt
	}
	sm.AverageResponseTime = total / time.Duration(len(sm.ResponseTimes))
}

// RecordError records an error
func (sm *ServiceMetrics) RecordError(endpoint, errorType, message string, statusCode int) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.ErrorsByType[errorType]++
	sm.ErrorsByEndpoint[endpoint]++

	// Add to recent errors
	errorRecord := ErrorRecord{
		Timestamp:  time.Now(),
		Endpoint:   endpoint,
		ErrorType:  errorType,
		Message:    message,
		StatusCode: statusCode,
	}

	sm.RecentErrors = append(sm.RecentErrors, errorRecord)
	if len(sm.RecentErrors) > 100 {
		sm.RecentErrors = sm.RecentErrors[1:] // Keep only last 100
	}
}

// RecordRetry records a retry operation
func (sm *ServiceMetrics) RecordRetry(endpoint string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.TotalRetries++
	sm.RetriesByEndpoint[endpoint]++
}

// RecordCacheHit records a cache hit
func (sm *ServiceMetrics) RecordCacheHit() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.CacheHits++
	sm.updateCacheHitRate()
}

// RecordCacheMiss records a cache miss
func (sm *ServiceMetrics) RecordCacheMiss() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.CacheMisses++
	sm.updateCacheHitRate()
}

// updateCacheHitRate calculates the cache hit rate
func (sm *ServiceMetrics) updateCacheHitRate() {
	total := sm.CacheHits + sm.CacheMisses
	if total > 0 {
		sm.CacheHitRate = float64(sm.CacheHits) / float64(total) * 100
	}
}

// UpdateHealthStatus updates the health status
func (sm *ServiceMetrics) UpdateHealthStatus(healthy bool) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.IsHealthy = healthy
	sm.LastHealthCheck = time.Now()
}

// UpdateUptime updates the uptime string
func (sm *ServiceMetrics) UpdateUptime() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	uptime := time.Since(sm.StartTime)
	sm.Uptime = uptime.String()
}

// GetSnapshot returns a snapshot of current metrics
func (sm *ServiceMetrics) GetSnapshot() *ServiceMetrics {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// Create a copy to avoid concurrent access issues
	snapshot := &ServiceMetrics{
		IsHealthy:           sm.IsHealthy,
		LastHealthCheck:     sm.LastHealthCheck,
		StartTime:           sm.StartTime,
		Uptime:              sm.Uptime,
		TotalRequests:       sm.TotalRequests,
		SuccessfulRequests:  sm.SuccessfulRequests,
		FailedRequests:      sm.FailedRequests,
		AverageResponseTime: sm.AverageResponseTime,
		MinResponseTime:     sm.MinResponseTime,
		MaxResponseTime:     sm.MaxResponseTime,
		CacheHits:           sm.CacheHits,
		CacheMisses:         sm.CacheMisses,
		CacheHitRate:        sm.CacheHitRate,
		CacheSize:           sm.CacheSize,
		MemoryOperations:    sm.MemoryOperations,
		ChatReports:         sm.ChatReports,
		ConfigRefreshCount:  sm.ConfigRefreshCount,
		RateLimitedRequests: sm.RateLimitedRequests,
		TotalRetries:        sm.TotalRetries,
	}

	// Copy maps
	snapshot.RequestsByEndpoint = make(map[string]int64)
	for k, v := range sm.RequestsByEndpoint {
		snapshot.RequestsByEndpoint[k] = v
	}

	snapshot.ErrorsByType = make(map[string]int64)
	for k, v := range sm.ErrorsByType {
		snapshot.ErrorsByType[k] = v
	}

	snapshot.ErrorsByEndpoint = make(map[string]int64)
	for k, v := range sm.ErrorsByEndpoint {
		snapshot.ErrorsByEndpoint[k] = v
	}

	snapshot.RetriesByEndpoint = make(map[string]int64)
	for k, v := range sm.RetriesByEndpoint {
		snapshot.RetriesByEndpoint[k] = v
	}

	// Copy recent errors (last 10)
	snapshot.RecentErrors = make([]ErrorRecord, 0)
	start := len(sm.RecentErrors) - 10
	if start < 0 {
		start = 0
	}
	for i := start; i < len(sm.RecentErrors); i++ {
		snapshot.RecentErrors = append(snapshot.RecentErrors, sm.RecentErrors[i])
	}

	return snapshot
}

// APIResponse represents a generic API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Code    string      `json:"code,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Healthy    bool                       `json:"healthy"`
	Message    string                     `json:"message"`
	Timestamp  time.Time                  `json:"timestamp"`
	Duration   time.Duration              `json:"duration"`
	Components map[string]ComponentHealth `json:"components"`
}

// ComponentHealth represents the health status of a component
type ComponentHealth struct {
	Healthy   bool      `json:"healthy"`
	Message   string    `json:"message"`
	LastCheck time.Time `json:"last_check"`
}
