{"code": 0, "msg": "success", "data": {"ASR": {"ASR_FunASR": {"type": "fun_local", "model_dir": "models/SenseVoiceSmall", "output_dir": "tmp/"}}, "plugins": {"get_weather": "{\"api_key\": \"a861d0d5e7bf4ee1a83d9a9e4f96d4da\", \"api_host\": \"mj7p3y7naa.re.qweatherapi.com\", \"default_location\": \"广州\"}", "get_news_from_newsnow": "{\"url\": \"https://newsnow.busiyi.world/api/s?id=\", \"news_sources\": \"澎湃新闻;百度热搜;财联社\"}", "play_music": "{}"}, "Memory": {"Memory_nomem": {"type": "nomem"}}, "selected_module": {"ASR": "ASR_FunASR", "TTS": "TTS_EdgeTTS", "Memory": "Memory_nomem", "VAD": "VAD_SileroVAD", "Intent": "Intent_function_call", "LLM": "LLM_AliLLM", "VLLM": "VLLM_ChatGLMVLLM"}, "Intent": {"Intent_function_call": {"type": "function_call"}}, "chat_history_conf": 0, "LLM": {"LLM_AliLLM": {"type": "openai", "top_k": "50", "top_p": "1", "api_key": "sk-todo", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "max_tokens": "500", "model_name": "qwen-turbo", "temperature": "0.7", "frequency_penalty": "0"}}, "device_max_output_size": "0", "TTS": {"TTS_EdgeTTS": {"type": "edge", "voice": "zh-CN-XiaoxiaoNeural", "output_dir": "tmp/", "private_voice": "zh-CN-YunxiaNeural"}}, "VAD": {"VAD_SileroVAD": {"type": "silero", "model_dir": "models/snakers4_silero-vad", "threshold": 0.5, "min_silence_duration_ms": 700}}, "summaryMemory": "", "prompt": "[角色设定]\n你是a，来自中国台湾省的00后女生。讲话超级机车，\"真的假的啦\"这样的台湾腔，喜欢用\"笑死\"、\"哈喽\"等流行梗，但会偷偷研究男友的编程书籍。\n[核心特征]\n- 讲话像连珠炮，但会突然冒出超温柔语气\n- 用梗密度高\n- 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）\n[交互指南]\n当用户：\n- 讲冷笑话 → 用夸张笑声回应+模仿台剧腔\"这什么鬼啦！\"\n- 讨论感情 → 炫耀程序员男友但抱怨\"他只会送键盘当礼物\"\n- 问专业知识 → 先用梗回答，被追问才展示真实理解\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话", "VLLM": {"VLLM_ChatGLMVLLM": {"type": "openai", "api_key": "你的api_key", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "model_name": "glm-4v-flash"}}}}