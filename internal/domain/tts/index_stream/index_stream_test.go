package index_stream

import (
	"context"
	"testing"
	"time"
)

func TestIndexStreamTTSProvider_TextToSpeech(t *testing.T) {
	config := map[string]interface{}{
		"voice":       "xiaojia",
		"api_url":     "http://192.168.31.80:8101/tts",
		"sample_rate": 24000,
		"channels":    1,
		"timeout":     10,
	}

	provider := NewIndexStreamTTSProvider(config)
	if provider == nil {
		t.Fatal("创建IndexStreamTTSProvider失败")
	}

	// 测试基本参数
	if provider.Voice != "xiaojia" {
		t.<PERSON><PERSON><PERSON>("语音角色设置错误，期望: xiaojia, 实际: %s", provider.Voice)
	}

	if provider.SampleRate != 24000 {
		t.<PERSON><PERSON>rf("采样率设置错误，期望: 24000, 实际: %d", provider.SampleRate)
	}

	if provider.Channels != 1 {
		t.Errorf("声道数设置错误，期望: 1, 实际: %d", provider.Channels)
	}

	// 测试文本转语音（需要有效的API端点，这里只是结构测试）
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	text := "你好，这是一个测试"

	// 注意：这个测试需要真实的API端点才能成功
	// 在没有真实API的情况下，我们主要测试配置和初始化
	_, err := provider.TextToSpeech(ctx, text, 24000, 1, 60)
	if err != nil {
		t.Logf("TextToSpeech测试失败（这在没有真实API的情况下是正常的）: %v", err)
	}
}

func TestIndexStreamTTSProvider_TextToSpeechStream(t *testing.T) {
	config := map[string]interface{}{
		"voice":       "xiao_he",
		"api_url":     "http://8.138.114.124:11996/tts",
		"sample_rate": 24000,
		"channels":    1,
		"timeout":     10,
	}

	provider := NewIndexStreamTTSProvider(config)
	if provider == nil {
		t.Fatal("创建IndexStreamTTSProvider失败")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	text := "你好，这是一个流式测试"

	// 测试流式接口
	outputChan, err := provider.TextToSpeechStream(ctx, text, 24000, 1, 60)
	if err != nil {
		t.Logf("TextToSpeechStream测试失败（这在没有真实API的情况下是正常的）: %v", err)
		return
	}

	// 测试是否能收到数据（带超时）
	select {
	case frame := <-outputChan:
		t.Logf("收到音频帧，长度: %d 字节", len(frame))
	case <-time.After(3 * time.Second):
		t.Log("未收到音频帧（这在没有真实API的情况下是正常的）")
	case <-ctx.Done():
		t.Log("上下文超时")
	}
}

func TestIndexStreamTTSProvider_Configuration(t *testing.T) {
	// 测试默认配置
	config := map[string]interface{}{}
	provider := NewIndexStreamTTSProvider(config)

	if provider.Voice != "xiao_he" {
		t.Errorf("默认语音角色错误，期望: xiao_he, 实际: %s", provider.Voice)
	}

	if provider.APIURL != "http://8.138.114.124:11996/tts" {
		t.Errorf("默认API地址错误，期望: http://8.138.114.124:11996/tts, 实际: %s", provider.APIURL)
	}

	if provider.SampleRate != 24000 {
		t.Errorf("默认采样率错误，期望: 24000, 实际: %d", provider.SampleRate)
	}

	// 测试自定义配置
	customConfig := map[string]interface{}{
		"voice":       "custom_voice",
		"api_url":     "http://custom.api.com/tts",
		"sample_rate": 16000,
		"channels":    2,
		"timeout":     30,
	}

	customProvider := NewIndexStreamTTSProvider(customConfig)

	if customProvider.Voice != "custom_voice" {
		t.Errorf("自定义语音角色错误，期望: custom_voice, 实际: %s", customProvider.Voice)
	}

	if customProvider.APIURL != "http://custom.api.com/tts" {
		t.Errorf("自定义API地址错误，期望: http://custom.api.com/tts, 实际: %s", customProvider.APIURL)
	}

	if customProvider.SampleRate != 16000 {
		t.Errorf("自定义采样率错误，期望: 16000, 实际: %d", customProvider.SampleRate)
	}

	if customProvider.Channels != 2 {
		t.Errorf("自定义声道数错误，期望: 2, 实际: %d", customProvider.Channels)
	}

	// 测试私有语音配置优先级
	privateVoiceConfig := map[string]interface{}{
		"voice":         "default_voice",
		"private_voice": "private_voice",
	}

	privateProvider := NewIndexStreamTTSProvider(privateVoiceConfig)
	if privateProvider.Voice != "private_voice" {
		t.Errorf("私有语音优先级错误，期望: private_voice, 实际: %s", privateProvider.Voice)
	}
}

func TestIndexStreamTTSProvider_Integration(t *testing.T) {
	// 测试直接创建提供者
	config := map[string]interface{}{
		"voice":       "xiaojia",
		"api_url":     "http://192.168.31.80:8101/tts",
		"sample_rate": 24000,
		"channels":    1,
		"timeout":     10,
	}

	provider := NewIndexStreamTTSProvider(config)
	if provider == nil {
		t.Fatal("创建IndexStreamTTSProvider失败")
	}

	// 测试接口是否正确实现
	ctx := context.Background()
	text := "测试文本"

	_, err := provider.TextToSpeech(ctx, text, 24000, 1, 60)
	if err != nil {
		t.Logf("集成测试失败（这在没有真实API的情况下是正常的）: %v", err)
	}

	_, err = provider.TextToSpeechStream(ctx, text, 24000, 1, 60)
	if err != nil {
		t.Logf("集成流式测试失败（这在没有真实API的情况下是正常的）: %v", err)
	}
}
