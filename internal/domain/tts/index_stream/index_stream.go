package index_stream

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/registry/tts"
	"xiaozhi-esp32-server-golang/internal/util"
	log "xiaozhi-esp32-server-golang/logger"
)

// IndexStreamTTSProvider Index Stream TTS 提供者
// 支持流式处理PCM音频数据并编码为Opus格式
type IndexStreamTTSProvider struct {
	APIURL string // TTS API 地址
	Voice  string // 语音角色

	RefAudios string // 少样本音频文件路径
	Seed      int

	AudioFormat string        // 音频格式，默认为pcm
	SampleRate  int           // 采样率，默认24000
	Channels    int           // 声道数，默认1
	Timeout     time.Duration // 请求超时时间

	// HTTP 客户端
	client *http.Client
}

// TTSRequest TTS请求结构体
type TTSRequest struct {
	Text      string `json:"text"`
	Character string `json:"character"`
}

type TTSRefRequest struct {
	Text      string   `json:"text"`
	RefAudios []string `json:"audio_paths"`
	Seed      int      `json:"seed"`
}

// init 函数注册GPT-SoVITS V2 TTS提供者
func init() {
	// 注册 GPT-SoVITS V2 TTS 提供者
	tts.Register([]string{"TTS_GPT_SOVITS_V2", constants.TtsTypeGptSovitsV2}, "GPT-SoVITS V2 TTS 语音合成服务", func(config map[string]interface{}) (tts.BaseTTSProvider, error) {
		provider := NewIndexStreamTTSProvider(config)
		return provider, nil
	})
}

// NewIndexStreamTTSProvider 创建IndexStreamTTSProvider
func NewIndexStreamTTSProvider(config map[string]interface{}) *IndexStreamTTSProvider {

	helper := util.NewConfigHelper(config)
	refAudios := helper.GetString("ref_audio")
	voice := helper.GetString("voice", "xiao_he")
	apiURL := helper.GetString("api_url")
	if apiURL == "" {
		apiURL = helper.GetString("url")
	}
	audioFormat := helper.GetString("audio_format", "wav")
	sampleRate := helper.GetInt("sample_rate", 24000)
	channels := helper.GetInt("channels", 1)
	seed := helper.GetInt("seed", 8)
	timeout := helper.GetInt("timeout", 10)
	privateVoice := helper.GetString("private_voice")
	if privateVoice != "" {
		voice = privateVoice
	}

	log.Debugf("IndexStream TTS API URL: %s config: %v", apiURL, config)

	return &IndexStreamTTSProvider{
		RefAudios:   refAudios,
		Seed:        seed,
		Voice:       voice,
		APIURL:      apiURL,
		AudioFormat: audioFormat,
		SampleRate:  sampleRate,
		Channels:    channels,
		Timeout:     time.Duration(timeout) * time.Second,
		client: &http.Client{
			Timeout: time.Duration(timeout) * time.Second,
		},
	}
}

// TextToSpeech 一次性合成，返回Opus帧
func (p *IndexStreamTTSProvider) TextToSpeech(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) ([][]byte, error) {
	startTs := time.Now().UnixMilli()
	log.Infof("IndexStream TTS开始合成文本: %s", text)

	var payload any
	if p.RefAudios != "" {
		payload = TTSRefRequest{
			Text:      text,
			RefAudios: strings.Split(p.RefAudios, ","),
			Seed:      p.Seed,
		}
	} else {
		payload = TTSRequest{
			Text:      text,
			Character: p.Voice,
		}
	}

	audioFormat, body, err := p.doPost(ctx, payload)
	outputChan := make(chan []byte, 1000)
	decoder, err := util.CreateAudioDecoder(ctx, body, outputChan, frameDuration, audioFormat)
	if err != nil {
		return nil, fmt.Errorf("创建解码器失败: %v", err)
	}

	var opusFrames [][]byte
	done := make(chan struct{})
	go func() {
		for frame := range outputChan {
			opusFrames = append(opusFrames, frame)
		}
		done <- struct{}{}
	}()
	if err := decoder.Run(startTs); err != nil {
		return nil, fmt.Errorf("MP3解码失败: %v", err)
	}
	<-done
	return opusFrames, nil
}

func (p *IndexStreamTTSProvider) doPost(ctx context.Context, payload any) (string, io.ReadCloser, error) {
	start := time.Now()
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", p.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", nil, fmt.Errorf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := p.client.Do(req)
	if err != nil {
		return "", nil, fmt.Errorf("TTS请求失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		return "", nil, fmt.Errorf("TTS请求[%s]失败，参数[%s], 状态码[%d], 返回内容：%s", p.APIURL, string(jsonData), resp.StatusCode, string(body))
	}

	audioFormat := util.GetAudioFormatByMimeType(resp.Header.Get("Content-Type"))

	log.Debugf("TTS请求URL:[%s], 参数:[%s] 音频格式:%s 耗时: %d ms", p.APIURL, string(jsonData), audioFormat, time.Now().UnixMilli()-start.UnixMilli())

	return audioFormat, resp.Body, nil
}

func (p *IndexStreamTTSProvider) doLocal(ctx context.Context, payload any) (string, io.ReadCloser, error) {
	f, err := os.Open("2025-08-26T142659.200.wav")
	return "wav", f, err
}

// TextToSpeechStream 流式合成，返回Opus帧chan
func (p *IndexStreamTTSProvider) TextToSpeechStream(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) (chan []byte, error) {
	startTs := time.Now().UnixMilli()
	log.Infof("IndexStream TTS开始流式合成文本: %s", text)

	var payload any
	if p.RefAudios != "" {
		payload = TTSRefRequest{
			Text:      text,
			RefAudios: strings.Split(p.RefAudios, ","),
			Seed:      p.Seed,
		}
	} else {
		payload = TTSRequest{
			Text:      text,
			Character: p.Voice,
		}
	}

	audioFormat, body, err := p.doPost(ctx, payload)
	if err != nil {
		return nil, fmt.Errorf("TTS请求失败: %v", err)
	}
	data, err := io.ReadAll(body)
	body.Close()

	//os.MkdirAll("/tmp/tts/index_stream", 0755)
	//os.WriteFile(fmt.Sprintf("/tmp/tts/index_stream/%s.wav", text), data, 0644)
	body = io.NopCloser(bytes.NewReader(data))

	outputChan := make(chan []byte, 1000)
	// 启动音频解码
	go func() {
		defer body.Close()
		// 创建音频解码器，根据检测到的格式设置
		audioDecoder, err := util.CreateAudioDecoder(ctx, body, outputChan, frameDuration, audioFormat)
		if err != nil {
			log.Errorf("IndexStream 音频解码器创建失败: %v", err)
			return
		}

		if err := audioDecoder.Run(startTs); err != nil {
			log.Errorf("IndexStream 音频解码失败: %v", err)
		}

		log.Debugf("IndexStream 音频解码结束, 耗时: %d ms", time.Now().UnixMilli()-startTs)
	}()

	return outputChan, nil
}
