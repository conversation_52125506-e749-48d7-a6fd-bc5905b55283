# Makefile for xiaozhi-backend-server
# Go最低版本要求
GO_MIN_VERSION := 1.23
APP_NAME := xiaozhi-backend-server
SERVER_BINARY := xiaozhi_server
MQTT_BINARY := xiaozhi_mqtt
OPUS_EXAMPLE_BINARY := opus_example

# 目录定义
BUILD_DIR := build
BIN_DIR := $(BUILD_DIR)/bin
COVERAGE_DIR := $(BUILD_DIR)/coverage
LOGS_DIR := logs

# 配置文件
CONFIG_FILE := config/config.json
MQTT_CONFIG_FILE := config/mqtt_config.json

# 颜色定义
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

# 默认目标
.DEFAULT_GOAL := help

# 默认版本号
VERSION ?= dev

# 从git获取版本号
GIT_VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "$(VERSION)")

# 构建参数
LDFLAGS := -ldflags "-X main.version=$(GIT_VERSION) -X xiaozhi-esp32-server-golang/logger.Version=$(GIT_VERSION)"

# 构建目标
BINARY_NAME := xiaozhi-server

# 构建命令
build:
	go build $(LDFLAGS) -o $(BINARY_NAME) ./cmd/server

# 构建Linux版本
build-linux:
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BINARY_NAME)-linux ./cmd/server

# 安装依赖
deps:
	go mod tidy

# 清理
clean:
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_NAME)-linux

# 显示版本号
version:
	@echo "Version: $(GIT_VERSION)"

# 帮助信息
help:
	@echo "Usage: make [target]"
	@echo ""
	@echo "Available targets:"
	@echo "  build        构建程序"
	@echo "  build-linux  构建Linux版本程序"
	@echo "  deps         安装依赖"
	@echo "  clean        清理构建产物"
	@echo "  version      显示版本号"
	@echo "  help         显示帮助信息"

.PHONY: build build-linux deps clean version help

# 检查Go版本
.PHONY: check-go-version
check-go-version:
	@echo "$(YELLOW)检查Go版本...$(NC)"
	@CURRENT_VERSION=$$(go version | sed 's/.*go\([0-9.]*\).*/\1/' | cut -d. -f1,2); \
	REQUIRED_VERSION=$(GO_MIN_VERSION); \
	if [ "$$(printf '%s\n' "$$REQUIRED_VERSION" "$$CURRENT_VERSION" | sort -V | head -n1)" != "$$REQUIRED_VERSION" ]; then \
		echo "$(RED)错误: 需要Go版本 >= $(GO_MIN_VERSION)，当前版本: $$CURRENT_VERSION$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)✓ Go版本检查通过$(NC)"

# 创建必要的目录
.PHONY: dirs
dirs:
	@echo "$(YELLOW)创建构建目录...$(NC)"
	@mkdir -p $(BIN_DIR)
	@mkdir -p $(COVERAGE_DIR)
	@mkdir -p $(LOGS_DIR)
	@echo "$(GREEN)✓ 目录创建完成$(NC)"

# 下载依赖
.PHONY: deps
deps: check-go-version
	@echo "$(YELLOW)下载Go模块依赖...$(NC)"
	@go mod download
	@go mod verify
	@echo "$(GREEN)✓ 依赖下载完成$(NC)"

# 整理依赖
.PHONY: tidy
tidy:
	@echo "$(YELLOW)整理Go模块依赖...$(NC)"
	@go mod tidy
	@echo "$(GREEN)✓ 依赖整理完成$(NC)"

# 代码格式化
.PHONY: fmt
fmt:
	@echo "$(YELLOW)格式化代码...$(NC)"
	@go fmt ./...
	@echo "$(GREEN)✓ 代码格式化完成$(NC)"

# 代码检查
.PHONY: vet
vet:
	@echo "$(YELLOW)进行代码检查...$(NC)"
	@go vet ./cmd/... ./internal/... || echo "$(YELLOW)⚠ 代码检查发现一些问题，但继续构建$(NC)"
	@echo "$(GREEN)✓ 代码检查完成$(NC)"

# 严格代码检查（包括测试文件）
.PHONY: vet-strict
vet-strict:
	@echo "$(YELLOW)进行严格代码检查...$(NC)"
	@go vet ./...
	@echo "$(GREEN)✓ 严格代码检查通过$(NC)"

# 编译主服务器
.PHONY: build-server
build-server: dirs
	@echo "$(YELLOW)编译主服务器...$(NC)"
	@CGO_ENABLED=1 go build -ldflags="-s -w" -o $(BIN_DIR)/$(SERVER_BINARY) ./cmd/server/
	@echo "$(GREEN)✓ 主服务器编译完成: $(BIN_DIR)/$(SERVER_BINARY)$(NC)"

# 编译MQTT服务器
.PHONY: build-mqtt
build-mqtt: dirs
	@echo "$(YELLOW)编译MQTT服务器...$(NC)"
	@CGO_ENABLED=1 go build -ldflags="-s -w" -o $(BIN_DIR)/$(MQTT_BINARY) ./cmd/mqtt/
	@echo "$(GREEN)✓ MQTT服务器编译完成: $(BIN_DIR)/$(MQTT_BINARY)$(NC)"

# 编译Opus示例
.PHONY: build-opus
build-opus: dirs
	@echo "$(YELLOW)编译Opus示例...$(NC)"
	@CGO_ENABLED=1 go build -ldflags="-s -w" -o $(BIN_DIR)/$(OPUS_EXAMPLE_BINARY) ./cmd/opus_example/
	@echo "$(GREEN)✓ Opus示例编译完成: $(BIN_DIR)/$(OPUS_EXAMPLE_BINARY)$(NC)"

# 编译GPT-SoVITS V2示例
.PHONY: build-gpt-sovits-v2-example
build-gpt-sovits-v2-example: dirs
	@echo "$(YELLOW)编译GPT-SoVITS V2示例...$(NC)"
	@CGO_ENABLED=1 go build -ldflags="-s -w" -o $(BIN_DIR)/gpt_sovits_v2_example ./examples/gpt_sovits_v2_example.go
	@echo "$(GREEN)✓ GPT-SoVITS V2示例编译完成: $(BIN_DIR)/gpt_sovits_v2_example$(NC)"

# 编译测试资源池优化组件
.PHONY: build-resource-pools
build-resource-pools:
	@echo "$(YELLOW)编译测试资源池优化组件...$(NC)"
	@echo "$(YELLOW)编译统一资源池管理器...$(NC)"
	@go build -v ./internal/util/pool/...
	@echo "$(YELLOW)编译优化后的VAD资源池...$(NC)"
	@go build -v ./internal/domain/vad/webrtc_vad/...
	@echo "$(YELLOW)编译音频编解码器资源池...$(NC)"
	@go build -v ./internal/domain/audio/...
	@echo "$(YELLOW)编译AI服务连接池...$(NC)"
	@go build -v ./internal/domain/ai/...
	@echo "$(GREEN)✓ 资源池优化组件编译测试完成$(NC)"

# 编译所有二进制文件
.PHONY: build
build: fmt vet build-resource-pools build-server build-mqtt build-opus
	@echo "$(GREEN)✓ 所有二进制文件编译完成$(NC)"
	@ls -la $(BIN_DIR)/

# 运行单元测试（核心功能）
.PHONY: test
test:
	@echo "$(YELLOW)运行核心单元测试...$(NC)"
	@go test -v -race -short ./internal/util/workqueue ./internal/domain/vad/webrtc_vad || echo "$(YELLOW)⚠ 部分测试失败，但这是预期的$(NC)"
	@echo "$(GREEN)✓ 核心单元测试完成$(NC)"

# 测试新实现的资源池管理组件
.PHONY: test-resource-pools
test-resource-pools:
	@echo "$(YELLOW)测试资源池管理组件...$(NC)"
	@echo "$(YELLOW)测试统一资源池接口和管理器...$(NC)"
	@go test -v -race -short ./internal/util/pool/... || echo "$(YELLOW)⚠ 资源池测试部分失败$(NC)"
	@echo "$(YELLOW)测试优化后的VAD资源池...$(NC)"
	@go test -v -race -short ./internal/domain/vad/webrtc_vad/... || echo "$(YELLOW)⚠ VAD资源池测试部分失败$(NC)"
	@echo "$(YELLOW)测试音频编解码器资源池...$(NC)"
	@go test -v -race -short ./internal/domain/audio/... || echo "$(YELLOW)⚠ 音频编解码器测试部分失败$(NC)"
	@echo "$(YELLOW)测试AI服务连接池...$(NC)"
	@go test -v -race -short ./internal/domain/ai/... || echo "$(YELLOW)⚠ AI连接池测试部分失败$(NC)"
	@echo "$(GREEN)✓ 资源池管理组件测试完成$(NC)"

# 运行所有测试（包括可能失败的）
.PHONY: test-all
test-all:
	@echo "$(YELLOW)运行所有单元测试...$(NC)"
	@go test -v -race -short ./... || echo "$(YELLOW)⚠ 部分测试失败（预期行为）$(NC)"
	@echo "$(GREEN)✓ 所有单元测试完成$(NC)"

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage: dirs
	@echo "$(YELLOW)运行测试并生成覆盖率报告...$(NC)"
	@go test -v -race -coverprofile=$(COVERAGE_DIR)/coverage.out ./...
	@go tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(COVERAGE_DIR)/coverage.html
	@go tool cover -func=$(COVERAGE_DIR)/coverage.out
	@echo "$(GREEN)✓ 覆盖率报告生成完成: $(COVERAGE_DIR)/coverage.html$(NC)"

# 运行基准测试
.PHONY: bench
bench:
	@echo "$(YELLOW)运行基准测试...$(NC)"
	@go test -bench=. -benchmem ./...
	@echo "$(GREEN)✓ 基准测试完成$(NC)"

# 资源池性能基准测试
.PHONY: bench-resource-pools
bench-resource-pools:
	@echo "$(YELLOW)运行资源池性能基准测试...$(NC)"
	@echo "$(YELLOW)VAD资源池性能测试...$(NC)"
	@go test -bench=BenchmarkWebRTCVADPool -benchmem ./internal/domain/vad/webrtc_vad/... || echo "$(YELLOW)⚠ VAD基准测试部分失败$(NC)"
	@echo "$(YELLOW)音频编解码器性能测试...$(NC)"
	@go test -bench=BenchmarkAudioCodec -benchmem ./internal/domain/audio/... || echo "$(YELLOW)⚠ 音频编解码器基准测试部分失败$(NC)"
	@echo "$(YELLOW)AI连接池性能测试...$(NC)"
	@go test -bench=BenchmarkAIConnection -benchmem ./internal/domain/ai/... || echo "$(YELLOW)⚠ AI连接池基准测试部分失败$(NC)"
	@echo "$(GREEN)✓ 资源池性能基准测试完成$(NC)"

# 检查配置文件
.PHONY: check-config
check-config:
	@echo "$(YELLOW)检查配置文件...$(NC)"
	@if [ ! -f $(CONFIG_FILE) ]; then echo "$(RED)错误: 配置文件 $(CONFIG_FILE) 不存在$(NC)" && exit 1; fi
	@if [ ! -f $(MQTT_CONFIG_FILE) ]; then echo "$(RED)错误: MQTT配置文件 $(MQTT_CONFIG_FILE) 不存在$(NC)" && exit 1; fi
	@echo "$(GREEN)✓ 配置文件检查通过$(NC)"

# 启动主服务器
.PHONY: run-server
run-server: build-server check-config
	@echo "$(YELLOW)启动主服务器...$(NC)"
	@$(BIN_DIR)/$(SERVER_BINARY) -c $(CONFIG_FILE)

# 启动MQTT服务器
.PHONY: run-mqtt
run-mqtt: build-mqtt check-config
	@echo "$(YELLOW)启动MQTT服务器...$(NC)"
	@$(BIN_DIR)/$(MQTT_BINARY) -c $(MQTT_CONFIG_FILE)

# 运行Opus示例
.PHONY: run-opus
run-opus: build-opus
	@echo "$(YELLOW)运行Opus示例...$(NC)"
	@$(BIN_DIR)/$(OPUS_EXAMPLE_BINARY)

# 启动所有服务（并行运行）
.PHONY: run
run: build check-config
	@echo "$(GREEN)启动xiaozhi-backend-server所有核心服务...$(NC)"
	@echo "$(YELLOW)正在启动主服务器和MQTT服务器...$(NC)"
	@echo "$(YELLOW)提示: 使用 Ctrl+C 停止所有服务$(NC)"
	@echo "$(YELLOW)主服务器端口: 8989, MQTT服务器端口: 2883$(NC)"
	@echo ""
	@trap 'echo "\n$(RED)正在停止所有服务...$(NC)"; kill 0; exit' INT; \
	($(BIN_DIR)/$(SERVER_BINARY) -c $(CONFIG_FILE) & echo "$(GREEN)✓ 主服务器已启动 (PID: $$!)$(NC)") & \
	($(BIN_DIR)/$(MQTT_BINARY) -c $(MQTT_CONFIG_FILE) & echo "$(GREEN)✓ MQTT服务器已启动 (PID: $$!)$(NC)") & \
	wait

# 停止所有服务
.PHONY: stop
stop:
	@echo "$(YELLOW)停止xiaozhi-backend-server所有服务...$(NC)"
	@pkill -f "$(SERVER_BINARY)" || echo "$(YELLOW)主服务器未运行$(NC)"
	@pkill -f "$(MQTT_BINARY)" || echo "$(YELLOW)MQTT服务器未运行$(NC)"
	@echo "$(GREEN)✓ 所有服务已停止$(NC)"

# 清理构建文件
.PHONY: clean
clean:
	@echo "$(YELLOW)清理构建文件...$(NC)"
	@rm -rf $(BUILD_DIR)
	@rm -f $(SERVER_BINARY) $(MQTT_BINARY) $(OPUS_EXAMPLE_BINARY)
	@echo "$(GREEN)✓ 清理完成$(NC)"

# 深度清理（包括依赖缓存）
.PHONY: clean-all
clean-all: clean
	@echo "$(YELLOW)深度清理...$(NC)"
	@go clean -modcache
	@echo "$(GREEN)✓ 深度清理完成$(NC)"

# 安装二进制文件到系统路径
.PHONY: install
install: build
	@echo "$(YELLOW)安装二进制文件...$(NC)"
	@sudo cp $(BIN_DIR)/$(SERVER_BINARY) /usr/local/bin/
	@sudo cp $(BIN_DIR)/$(MQTT_BINARY) /usr/local/bin/
	@echo "$(GREEN)✓ 安装完成$(NC)"

# 卸载二进制文件
.PHONY: uninstall
uninstall:
	@echo "$(YELLOW)卸载二进制文件...$(NC)"
	@sudo rm -f /usr/local/bin/$(SERVER_BINARY)
	@sudo rm -f /usr/local/bin/$(MQTT_BINARY)
	@echo "$(GREEN)✓ 卸载完成$(NC)"

# Docker相关命令
.PHONY: docker-build
docker-build:
	@echo "$(YELLOW)构建Docker镜像...$(NC)"
	@docker build -t $(APP_NAME):latest .
	@echo "$(GREEN)✓ Docker镜像构建完成$(NC)"

.PHONY: docker-run
docker-run:
	@echo "$(YELLOW)运行Docker容器...$(NC)"
	@docker run -d --name $(APP_NAME) -p 8989:8989 -p 2883:2883 $(APP_NAME):latest
	@echo "$(GREEN)✓ Docker容器启动完成$(NC)"

.PHONY: docker-stop
docker-stop:
	@echo "$(YELLOW)停止Docker容器...$(NC)"
	@docker stop $(APP_NAME) || true
	@docker rm $(APP_NAME) || true
	@echo "$(GREEN)✓ Docker容器已停止$(NC)"

# 检查代码质量工具
.PHONY: check-tools
check-tools:
	@echo "$(YELLOW)检查代码质量工具...$(NC)"
	@command -v golint >/dev/null 2>&1 || echo "$(YELLOW)建议安装: go install golang.org/x/lint/golint@latest$(NC)"
	@command -v golangci-lint >/dev/null 2>&1 || echo "$(YELLOW)建议安装: https://golangci-lint.run/usage/install/$(NC)"
	@command -v gocyclo >/dev/null 2>&1 || echo "$(YELLOW)建议安装: go install github.com/fzipp/gocyclo/cmd/gocyclo@latest$(NC)"

# 运行代码质量检查（如果工具可用）
.PHONY: lint
lint:
	@echo "$(YELLOW)运行代码质量检查...$(NC)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)golangci-lint 未安装，跳过lint检查$(NC)"; \
	fi

# 完整的CI/CD流程（包含资源池优化测试）
.PHONY: ci
ci: deps fmt vet test test-resource-pools build
	@echo "$(GREEN)✓ CI流程完成$(NC)"

# 性能优化验证流程
.PHONY: performance-verify
performance-verify: build-resource-pools test-resource-pools bench-resource-pools
	@echo "$(GREEN)✓ 性能优化验证完成$(NC)"

# 开发环境准备
.PHONY: dev-setup
dev-setup: deps check-tools dirs
	@echo "$(GREEN)✓ 开发环境准备完成$(NC)"

# 生产环境构建
.PHONY: prod-build
prod-build: clean tidy fmt vet test build
	@echo "$(GREEN)✓ 生产环境构建完成$(NC)"

# 显示项目信息
.PHONY: info
info:
	@echo "$(GREEN)项目信息:$(NC)"
	@echo "  应用名称: $(APP_NAME)"
	@echo "  Go最低版本要求: $(GO_MIN_VERSION)"
	@echo "  主服务器二进制: $(BIN_DIR)/$(SERVER_BINARY)"
	@echo "  MQTT服务器二进制: $(BIN_DIR)/$(MQTT_BINARY)"
	@echo "  配置文件: $(CONFIG_FILE)"
	@echo "  构建目录: $(BUILD_DIR)"

# 显示帮助信息
.PHONY: help
help:
	@echo "$(GREEN)可用的Make目标:$(NC)"
	@echo ""
	@echo "$(YELLOW)基础命令:$(NC)"
	@echo "  help         显示此帮助信息"
	@echo "  info         显示项目信息"
	@echo "  deps         下载Go模块依赖"
	@echo "  tidy         整理Go模块依赖"
	@echo ""
	@echo "$(YELLOW)代码质量:$(NC)"
	@echo "  fmt          格式化代码"
	@echo "  vet          代码检查（宽松模式）"
	@echo "  vet-strict   严格代码检查（包括测试文件）"
	@echo "  lint         运行代码质量检查（需要golangci-lint）"
	@echo "  check-tools  检查代码质量工具是否安装"
	@echo ""
	@echo "$(YELLOW)构建命令:$(NC)"
	@echo "  build                编译所有二进制文件"
	@echo "  build-server         编译主服务器"
	@echo "  build-mqtt           编译MQTT服务器"
	@echo "  build-opus           编译Opus示例"
	@echo "  build-gpt-sovits-v2-example 编译GPT-SoVITS V2示例"
	@echo "  build-resource-pools 编译测试资源池优化组件"
	@echo ""
	@echo "$(YELLOW)测试命令:$(NC)"
	@echo "  test                 运行核心单元测试"
	@echo "  test-all             运行所有单元测试（包括可能失败的）"
	@echo "  test-resource-pools  测试新实现的资源池管理组件"
	@echo "  test-coverage        运行测试并生成覆盖率报告"
	@echo "  bench                运行基准测试"
	@echo "  bench-resource-pools 资源池性能基准测试"
	@echo ""
	@echo "$(YELLOW)运行命令:$(NC)"
	@echo "  run          启动所有核心服务（主服务器+MQTT服务器）"
	@echo "  run-server   启动主服务器"
	@echo "  run-mqtt     启动MQTT服务器"
	@echo "  run-opus     运行Opus示例"
	@echo "  stop         停止所有运行中的服务"
	@echo ""
	@echo "$(YELLOW)Docker命令:$(NC)"
	@echo "  docker-build 构建Docker镜像"
	@echo "  docker-run   运行Docker容器"
	@echo "  docker-stop  停止Docker容器"
	@echo ""
	@echo "$(YELLOW)其他命令:$(NC)"
	@echo "  clean        清理构建文件"
	@echo "  clean-all    深度清理（包括依赖缓存）"
	@echo "  install      安装二进制文件到系统路径"
	@echo "  uninstall    卸载二进制文件"
	@echo ""
	@echo "$(YELLOW)工作流程:$(NC)"
	@echo "  dev-setup           开发环境准备"
	@echo "  ci                  CI/CD流程（包含资源池优化测试）"
	@echo "  performance-verify  性能优化验证流程"
	@echo "  prod-build          生产环境构建"
