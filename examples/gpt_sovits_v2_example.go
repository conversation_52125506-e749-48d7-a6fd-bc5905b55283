package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/domain/tts"
)

func main() {
	// GPT-SoVITS V2 配置示例
	config := map[string]interface{}{
		"url":                 "http://127.0.0.1:9880/tts",
		"output_dir":          "tmp/",
		"text_lang":           "zh",
		"ref_audio_path":      "demo.wav",
		"prompt_text":         "你好，欢迎使用小智语音助手。",
		"prompt_lang":         "zh",
		"top_k":               float64(5),
		"top_p":               1.0,
		"temperature":         1.0,
		"text_split_method":   "cut0",
		"batch_size":          float64(1),
		"batch_threshold":     0.75,
		"split_bucket":        true,
		"return_fragment":     false,
		"speed_factor":        1.0,
		"streaming_mode":      false,
		"seed":                float64(-1),
		"parallel_infer":      true,
		"repetition_penalty":  1.35,
		"aux_ref_audio_paths": []interface{}{},
	}

	// 创建GPT-SoVITS V2 TTS提供者
	provider, err := tts.GetTTSProvider(constants.TtsTypeGptSovitsV2, config)
	if err != nil {
		log.Fatalf("创建GPT-SoVITS V2 TTS提供者失败: %v", err)
	}

	fmt.Println("GPT-SoVITS V2 TTS提供者创建成功!")

	// 显示提供者信息
	if voiceProvider, ok := provider.(*tts.ContextTTSAdapter); ok {
		if gptSovitsProvider, ok := voiceProvider.Provider.(interface {
			GetVoiceInfo() map[string]interface{}
		}); ok {
			voiceInfo := gptSovitsProvider.GetVoiceInfo()
			fmt.Printf("语音信息: %+v\n", voiceInfo)
		}
	}

	// 测试文本转语音（需要实际的GPT-SoVITS V2服务运行）
	fmt.Println("\n注意: 以下测试需要GPT-SoVITS V2服务在 http://127.0.0.1:9880 运行")
	fmt.Println("如果服务未运行，测试将失败，这是正常的。")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	testText := "你好，这是GPT-SoVITS V2的测试语音合成。"
	fmt.Printf("正在合成文本: %s\n", testText)

	// 尝试文本转语音
	frames, err := provider.TextToSpeech(ctx, testText, 24000, 1, 20)
	if err != nil {
		fmt.Printf("文本转语音失败 (这是预期的，如果GPT-SoVITS V2服务未运行): %v\n", err)
	} else {
		fmt.Printf("文本转语音成功! 生成了 %d 个音频帧\n", len(frames))

		// 计算总音频长度（估算）
		totalBytes := 0
		for _, frame := range frames {
			totalBytes += len(frame)
		}
		fmt.Printf("总音频数据大小: %d bytes\n", totalBytes)
	}

	// 测试流式文本转语音
	fmt.Println("\n测试流式文本转语音...")
	outputChan, err := provider.TextToSpeechStream(ctx, testText, 24000, 1, 20)
	if err != nil {
		fmt.Printf("流式文本转语音失败: %v\n", err)
	} else {
		frameCount := 0
		for frame := range outputChan {
			if len(frame) > 0 {
				frameCount++
			}
		}
		fmt.Printf("流式文本转语音完成! 接收到 %d 个音频帧\n", frameCount)
	}

	fmt.Println("\nGPT-SoVITS V2 TTS示例完成!")
}
