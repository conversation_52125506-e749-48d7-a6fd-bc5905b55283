package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/domain/tts"
)

func main() {
	// 配置IndexStream TTS提供者
	config := map[string]interface{}{
		"voice":       "xiao_he",                        // 语音角色
		"api_url":     "http://8.138.114.124:11996/tts", // TTS API地址
		"sample_rate": 24000,                            // 采样率
		"channels":    1,                                // 声道数
		"timeout":     10,                               // 超时时间(秒)
	}

	// 创建TTS提供者
	provider, err := tts.GetTTSProvider(constants.TtsTypeIndexStream, config)
	if err != nil {
		log.Fatalf("创建IndexStream TTS提供者失败: %v", err)
	}

	fmt.Println("=== IndexStream TTS 示例 ===")

	// 示例1: 一次性合成
	fmt.Println("\n1. 一次性合成示例")
	oneTimeExample(provider)

	// 示例2: 流式合成
	fmt.Println("\n2. 流式合成示例")
	streamExample(provider)
}

// oneTimeExample 一次性合成示例
func oneTimeExample(provider tts.TTSProvider) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	text := "你好，这是一个IndexStream TTS的一次性合成示例。"
	fmt.Printf("合成文本: %s\n", text)

	startTime := time.Now()
	frames, err := provider.TextToSpeech(ctx, text, 24000, 1, 60)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("合成失败: %v\n", err)
		return
	}

	fmt.Printf("合成成功！\n")
	fmt.Printf("- 耗时: %v\n", duration)
	fmt.Printf("- 生成帧数: %d\n", len(frames))

	totalBytes := 0
	for _, frame := range frames {
		totalBytes += len(frame)
	}
	fmt.Printf("- 总音频字节: %d\n", totalBytes)
}

// streamExample 流式合成示例
func streamExample(provider tts.TTSProvider) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	text := "这是一个IndexStream TTS的流式合成示例，可以实时接收音频数据。"
	fmt.Printf("流式合成文本: %s\n", text)

	startTime := time.Now()
	outputChan, err := provider.TextToSpeechStream(ctx, text, 24000, 1, 60)
	if err != nil {
		fmt.Printf("流式合成启动失败: %v\n", err)
		return
	}

	frameCount := 0
	totalBytes := 0
	firstFrameTime := time.Time{}

	// 接收音频帧
	for frame := range outputChan {
		frameCount++
		totalBytes += len(frame)

		if firstFrameTime.IsZero() {
			firstFrameTime = time.Now()
			fmt.Printf("收到首帧，延迟: %v\n", firstFrameTime.Sub(startTime))
		}

		if frameCount%10 == 0 {
			fmt.Printf("已收到 %d 帧，累计 %d 字节\n", frameCount, totalBytes)
		}
	}

	duration := time.Since(startTime)
	fmt.Printf("流式合成完成！\n")
	fmt.Printf("- 总耗时: %v\n", duration)
	fmt.Printf("- 总帧数: %d\n", frameCount)
	fmt.Printf("- 总字节: %d\n", totalBytes)

	if !firstFrameTime.IsZero() {
		fmt.Printf("- 首帧延迟: %v\n", firstFrameTime.Sub(startTime))
	}
}
