package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"xiaozhi-esp32-server-golang/internal/domain/manager_api"
)

func main() {
	// 示例配置
	config := &manager_api.Config{
		BaseURL:                    "http://localhost:8080",
		Secret:                     "your-secret-token",
		TimeoutSeconds:             30,
		RetryAttempts:              3,
		RetryDelayMs:               1000,
		MaxRetryDelayMs:            10000,
		Enabled:                    true,
		CacheTTLSeconds:            300,
		HealthCheckIntervalSeconds: 30,
		BatchSize:                  50,
		FlushIntervalSeconds:       10,
	}

	// 创建服务
	service, err := manager_api.NewService(config)
	if err != nil {
		log.Fatalf("Failed to create manager-api service: %v", err)
	}

	ctx := context.Background()

	// 启动服务
	if err := service.Start(ctx); err != nil {
		log.Fatalf("Failed to start service: %v", err)
	}
	defer func() {
		if err := service.Stop(ctx); err != nil {
			log.Printf("Error stopping service: %v", err)
		}
	}()

	fmt.Println("Manager-API Service Example")
	fmt.Println("===========================")

	// 1. 获取服务器配置
	fmt.Println("\n1. Getting server configuration...")
	serverConfig, err := service.GetServerConfig(ctx)
	if err != nil {
		fmt.Printf("Error getting server config: %v\n", err)
	} else {
		configJSON, _ := json.MarshalIndent(serverConfig, "", "  ")
		fmt.Printf("Server Config: %s\n", configJSON)
	}

	// 2. 获取设备配置
	fmt.Println("\n2. Getting device configuration...")
	deviceConfig, err := service.GetDeviceConfig(ctx, "aa:bb:cc:dd:ee:ff", "test-client", map[string]string{
		"VAD": "webrtc_vad",
		"ASR": "funasr",
		"LLM": "qwen2.5",
		"TTS": "edge",
	})
	if err != nil {
		fmt.Printf("Error getting device config: %v\n", err)
	} else {
		deviceJSON, _ := json.MarshalIndent(deviceConfig, "", "  ")
		fmt.Printf("Device Config: %s\n", deviceJSON)
	}

	// 3. 保存设备内存
	fmt.Println("\n3. Saving device memory...")
	err = service.SaveDeviceMemory(ctx, "aa:bb:cc:dd:ee:ff", "User prefers concise responses. Interested in technology topics.")
	if err != nil {
		fmt.Printf("Error saving device memory: %v\n", err)
	} else {
		fmt.Println("Device memory saved successfully")
	}

	// 4. 获取设备内存
	fmt.Println("\n4. Getting device memory...")
	memory, err := service.GetDeviceMemory(ctx, "aa:bb:cc:dd:ee:ff")
	if err != nil {
		fmt.Printf("Error getting device memory: %v\n", err)
	} else {
		fmt.Printf("Device Memory: %s\n", memory)
	}

	// 5. 报告聊天历史
	fmt.Println("\n5. Reporting chat history...")
	err = service.ReportChat(ctx, "aa:bb:cc:dd:ee:ff", "session-123", "user", "Hello, how are you today?", nil)
	if err != nil {
		fmt.Printf("Error reporting chat: %v\n", err)
	} else {
		fmt.Println("Chat history reported successfully")
	}

	// 6. 检查健康状态
	fmt.Println("\n6. Checking service health...")
	isHealthy := service.IsHealthy()
	fmt.Printf("Service is healthy: %v\n", isHealthy)

	// 7. 获取服务指标
	fmt.Println("\n7. Getting service metrics...")
	metrics := service.GetMetrics()
	fmt.Printf("Total requests: %d\n", metrics.TotalRequests)
	fmt.Printf("Successful requests: %d\n", metrics.SuccessfulRequests)
	fmt.Printf("Failed requests: %d\n", metrics.FailedRequests)
	fmt.Printf("Cache hit rate: %.2f%%\n", metrics.CacheHitRate)
	fmt.Printf("Memory operations: %d\n", metrics.MemoryOperations)
	fmt.Printf("Chat reports: %d\n", metrics.ChatReports)

	// 8. 刷新配置缓存
	fmt.Println("\n8. Refreshing configuration cache...")
	err = service.RefreshConfig(ctx)
	if err != nil {
		fmt.Printf("Error refreshing config: %v\n", err)
	} else {
		fmt.Println("Configuration cache refreshed successfully")
	}

	// 等待一段时间以观察后台任务
	fmt.Println("\n9. Waiting for background tasks...")
	time.Sleep(2 * time.Second)

	// 最终指标
	finalMetrics := service.GetMetrics()
	fmt.Printf("\nFinal Metrics:\n")
	fmt.Printf("- Uptime: %s\n", finalMetrics.Uptime)
	fmt.Printf("- Config refresh count: %d\n", finalMetrics.ConfigRefreshCount)
	fmt.Printf("- Cache size: %d\n", finalMetrics.CacheSize)

	fmt.Println("\nManager-API service example completed successfully!")
}
