package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/domain/tts"
)

func main() {
	// GPT-SoVITS V3 配置示例
	config := map[string]interface{}{
		"url":             "http://127.0.0.1:9880",
		"refer_wav_path":  "demo.wav",
		"prompt_text":     "你好，欢迎使用小智语音助手。",
		"prompt_language": "zh",
		"text_language":   "zh",
		"top_k":           float64(5),
		"top_p":           1.0,
		"temperature":     1.0,
		"sample_steps":    float64(50),
		"speed":           1.0,
		"cut_punc":        "。？！.?!；;：:",
		"inp_refs":        "",
		"if_sr":           false,
		"audio_file_type": "wav",
	}

	// 创建GPT-SoVITS V3 TTS提供者
	provider, err := tts.GetTTSProvider(constants.TtsTypeGptSovitsV3, config)
	if err != nil {
		log.Fatalf("创建GPT-SoVITS V3 TTS提供者失败: %v", err)
	}

	fmt.Println("GPT-SoVITS V3 TTS提供者创建成功!")

	// 显示提供者信息
	if voiceProvider, ok := provider.(*tts.ContextTTSAdapter); ok {
		if gptSovitsProvider, ok := voiceProvider.Provider.(interface {
			GetVoiceInfo() map[string]interface{}
		}); ok {
			voiceInfo := gptSovitsProvider.GetVoiceInfo()
			fmt.Printf("语音信息: %+v\n", voiceInfo)
		}
	}

	// 测试文本转语音
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	testText := "你好，这是GPT-SoVITS V3的测试。使用GET请求方式进行语音合成。"
	fmt.Printf("正在转换文本: %s\n", testText)

	start := time.Now()
	frames, err := provider.TextToSpeech(ctx, testText, 24000, 1, 20)
	duration := time.Since(start)

	if err != nil {
		log.Printf("文本转语音失败: %v", err)
		fmt.Println("注意：此示例需要运行中的GPT-SoVITS V3服务")
		return
	}

	fmt.Printf("转换成功! 生成了 %d 个音频帧，耗时: %v\n", len(frames), duration)

	// 测试流式接口
	fmt.Println("\n开始测试流式接口...")
	outputChan, err := provider.TextToSpeechStream(ctx, "这是流式测试。", 24000, 1, 20)
	if err != nil {
		log.Printf("流式转换失败: %v", err)
		return
	}

	frameCount := 0
	start = time.Now()
	for frame := range outputChan {
		frameCount++
		fmt.Printf("接收到第 %d 个音频帧 (大小: %d 字节)\n", frameCount, len(frame))
	}
	duration = time.Since(start)

	fmt.Printf("流式转换完成! 总共接收 %d 个音频帧，耗时: %v\n", frameCount, duration)
}
